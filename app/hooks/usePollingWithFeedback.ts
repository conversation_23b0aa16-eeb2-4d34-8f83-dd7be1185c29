import { useState, useEffect, useRef } from 'react';
import { useNavigation, useRevalidator } from '@remix-run/react';
import { formatRelativeTime } from '../utils/formatting';

/**
 * The return type of our custom hook.
 */
interface PollingFeedback {
  /** A formatted string, e.g., "Last updated: just now" */
  formattedTime: string;
  /** A boolean to indicate if the flash animation should be active. */
  isFlashing: boolean;
}

/**
 * A custom hook that re-validates page data at a set interval and provides
 * visual feedback state for the UI.
 * @param intervalInMs The polling interval in milliseconds. Defaults to 5000.
 */
export function usePollingWithFeedback(intervalInMs: number = 5000): PollingFeedback {
  const revalidator = useRevalidator();
  const navigation = useNavigation();

  const [lastUpdated, setLastUpdated] = useState<Date>(() => new Date());
  const [isFlashing, setIsFlashing] = useState(false);

  // We use a ref to track the previous state of the revalidator to detect
  // when a fetch has just completed.
  const revalidatorStateRef = useRef(revalidator.state);

  // Effect to handle the core polling interval
  useEffect(() => {
    const interval = setInterval(() => {
      if (revalidator.state === 'idle' && navigation.state === 'idle') {
        revalidator.revalidate();
      }
    }, intervalInMs);

    return () => clearInterval(interval);
  }, [revalidator, navigation.state, intervalInMs]);

  // Effect to handle the visual feedback when data is re-validated
  useEffect(() => {
    // Check if the revalidation has just finished
    if (revalidator.state === 'idle' && revalidatorStateRef.current === 'loading') {
      setLastUpdated(new Date()); // Update the timestamp
      setIsFlashing(true); // Trigger the flash

      // Turn off the flash animation after it has had time to complete
      const timer = setTimeout(() => setIsFlashing(false), 2000);
      return () => clearTimeout(timer);
    }

    // Keep the ref updated with the current state for the next render
    revalidatorStateRef.current = revalidator.state;
  }, [revalidator.state]);


  return {
    formattedTime: `Last updated: ${formatRelativeTime(lastUpdated)}`,
    isFlashing,
  };
}