import { useEffect } from 'react';
import { useNavigation, useRevalidator } from '@remix-run/react';

/**
 * A custom hook that re-validates the page's data at a set interval.
 * @param intervalInMs The polling interval in milliseconds. Defaults to 5000.
 */
export function usePolling(intervalInMs: number = 5000) {
  const revalidator = useRevalidator();
  const navigation = useNavigation();

  useEffect(() => {
    const interval = setInterval(() => {
      // Only revalidate if the page is idle (no active navigation or revalidation)
      if (revalidator.state === 'idle' && navigation.state === 'idle') {
        revalidator.revalidate();
      }
    }, intervalInMs);

    // Cleanup on unmount
    return () => clearInterval(interval);
  }, [revalidator, navigation.state, intervalInMs]);
}