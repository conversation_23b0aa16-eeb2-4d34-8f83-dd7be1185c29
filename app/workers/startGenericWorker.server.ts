// app/workers/startGenericWorker.server.ts
import { startWorker } from "./genericWorker.server";

// This file serves as the entry point to start the generic worker.
// It imports the startWorker function from genericWorker.server.ts and executes it.
startWorker().catch((error: unknown) => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  console.error(`Failed to start generic worker:`, errorMessage);
});
