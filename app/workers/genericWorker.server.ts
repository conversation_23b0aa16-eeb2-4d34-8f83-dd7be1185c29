import { getNextPendingJob, updateJobStatus, retryJob } from "../queues/jobQueue.server";
import { logToFile } from "../utils/logger.server";
import { unauthenticated } from "../shopify.server";
import { handleInventoryUpdateJob } from "../jobHandlers/inventoryUpdateHandler.server";
import { handleOrderUtmTaggingJob } from "../jobHandlers/orderUtmTaggingHandler.server";
import { handleOrderCollectionTaggingJob } from "../jobHandlers/orderCollectionTaggingHandler.server";
import { handleOrderCartAttributeTaggingJob } from "../jobHandlers/orderCartAttributeTaggingHandler.server";
import { handleCustomerTaggingJob } from "../jobHandlers/customerTaggingHandler.server";
import { JobType } from "@prisma/client";
import { handleCollectionVisibilityBulkUpdateJob } from "../jobHandlers/collectionVisibilityBulkUpdateHandler.server";
import { handleStuckJobCleanup } from "../jobHandlers/stuckJobHandler.server";
import { customerVendorTaggingHandler } from "../jobHandlers/customerVendorTaggingHandler.server";
import { orderDiscountTaggingHandler } from "../jobHandlers/orderDiscountTaggingHandler.server";
import { handleCancelHighRiskOrderJob } from "../jobHandlers/cancelHighRiskOrderHandler.server";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import { generateWorkerId, createJobHandlerRegistry, createPoller, createJobProcessor } from "../utils/worker";
import { executeGraphQLWithRetry } from "../utils/shopify";

const WORKER_ID = generateWorkerId('generic-worker');

// 🦠 BACTERIAL APPROACH - Using registry instead of plain object
const handlerRegistry = createJobHandlerRegistry<JobType>();

// Register all job handlers using bacterial registry
handlerRegistry.register(JobType.COLLECTION_VISIBILITY_UPDATE, { handle: handleInventoryUpdateJob });
handlerRegistry.register(JobType.COLLECTION_VISIBILITY_BULK_UPDATE, { handle: handleCollectionVisibilityBulkUpdateJob });
handlerRegistry.register(JobType.AUTO_TAG_ORDERS_UTM, { handle: handleOrderUtmTaggingJob });
handlerRegistry.register(JobType.ORDER_COLLECTION_TAG, { handle: handleOrderCollectionTaggingJob });
handlerRegistry.register(JobType.ORDER_CART_ATTRIBUTE_TAG, { handle: handleOrderCartAttributeTaggingJob });
handlerRegistry.register(JobType.AUTO_TAG_CUSTOMER_BY_ORDER_TAG, { handle: handleCustomerTaggingJob });
handlerRegistry.register(JobType.AUTO_TAG_CUSTOMER_BY_VENDOR, { handle: customerVendorTaggingHandler });
handlerRegistry.register(JobType.STUCK_JOB_CLEANUP, { handle: handleStuckJobCleanup });
handlerRegistry.register(JobType.ORDER_DISCOUNT_TAG, { handle: orderDiscountTaggingHandler });
handlerRegistry.register(JobType.CANCEL_HIGH_RISK_ORDER, { handle: handleCancelHighRiskOrderJob });

// 🦠 BACTERIAL APPROACH - Using composed utilities instead of monolithic function
export async function shopifyGraphqlRequest(
  admin: any,
  query: string,
  variables: any,
  jobId: string,
  maxRetries = 5,
  baseDelayMs = 1000
): Promise<any> {
  return executeGraphQLWithRetry(admin, query, variables, {
    maxRetries,
    baseDelayMs,
    onRetry: (attempt, error) => {
      logToFile(jobId, "warn", `GraphQL request retry (attempt ${attempt}/${maxRetries}): ${error.message}`);
    }
  });
}

// 🦠 BACTERIAL APPROACH - Create logger interface for bacterial job processor
const logger = {
  info: (jobId: string, message: string) => logToFile(jobId, "info", message),
  error: (jobId: string, message: string) => logToFile(jobId, "error", message),
  warn: (jobId: string, message: string) => logToFile(jobId, "warn", message),
};

// 🦠 BACTERIAL APPROACH - Use composed job processor
const processJobWithProcessor = createJobProcessor(
  handlerRegistry,
  logger
);

async function processJob() {
  const job = await getNextPendingJob();

  if (job) {
    await processJobWithProcessor(
      job,
      async (shop: string) => unauthenticated.admin(shop),
      async (jobId: string, status: string, error?: string, startedAt?: Date) => {
        await updateJobStatus(jobId, status as any, error, startedAt);
      },
      retryJob
    );
  }
}

// 🦠 BACTERIAL APPROACH - Use bacterial poller utility
const jobPoller = createPoller(processJob, 5000);

export async function startWorker() {
  logToFile("worker-startup", "info", `Generic Worker ${WORKER_ID} started.`);
  jobPoller.start();
}

export async function stopWorker() {
  logToFile("worker-shutdown", "info", `Generic Worker ${WORKER_ID} stopping.`);
  jobPoller.stop();
}
