import { getNextPendingJob, updateJobStatus, retryJob } from "../queues/jobQueue.server";
import { logToFile } from "../utils/logger.server";
import { unauthenticated } from "../shopify.server";
import { handleInventoryUpdateJob } from "../jobHandlers/inventoryUpdateHandler.server";
import { handleOrderUtmTaggingJob } from "../jobHandlers/orderUtmTaggingHandler.server";
import { handleOrderCollectionTaggingJob } from "../jobHandlers/orderCollectionTaggingHandler.server";
import { handleOrderCartAttributeTaggingJob } from "../jobHandlers/orderCartAttributeTaggingHandler.server";
import { handleCustomerTaggingJob } from "../jobHandlers/customerTaggingHandler.server";
import { JobType } from "@prisma/client";
import { handleCollectionVisibilityBulkUpdateJob } from "../jobHandlers/collectionVisibilityBulkUpdateHandler.server";
import { handleStuckJobCleanup } from "../jobHandlers/stuckJobHandler.server";
import { customerVendorTaggingHandler } from "../jobHandlers/customerVendorTaggingHandler.server";
import { orderDiscountTaggingHandler } from "../jobHandlers/orderDiscountTaggingHandler.server";
import { handleCancelHighRiskOrderJob } from "../jobHandlers/cancelHighRiskOrderHandler.server";

const WORKER_ID = `generic-worker-${Math.random().toString(36).substring(2, 9)}`;

const jobHandlers: { [key: string]: { handle: (job: any, admin: any) => Promise<boolean> } } = {
  [JobType.COLLECTION_VISIBILITY_UPDATE]: { handle: handleInventoryUpdateJob },
  [JobType.COLLECTION_VISIBILITY_BULK_UPDATE]: { handle: handleCollectionVisibilityBulkUpdateJob },
  [JobType.AUTO_TAG_ORDERS_UTM]: { handle: handleOrderUtmTaggingJob },
  [JobType.ORDER_COLLECTION_TAG]: { handle: handleOrderCollectionTaggingJob },
  [JobType.ORDER_CART_ATTRIBUTE_TAG]: { handle: handleOrderCartAttributeTaggingJob },
  [JobType.AUTO_TAG_CUSTOMER_BY_ORDER_TAG]: { handle: handleCustomerTaggingJob },
  [JobType.AUTO_TAG_CUSTOMER_BY_VENDOR]: { handle: customerVendorTaggingHandler },
  [JobType.STUCK_JOB_CLEANUP]: { handle: handleStuckJobCleanup },
  [JobType.ORDER_DISCOUNT_TAG]: { handle: orderDiscountTaggingHandler },
  [JobType.CANCEL_HIGH_RISK_ORDER]: { handle: handleCancelHighRiskOrderJob },
};

function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export async function shopifyGraphqlRequest(
  admin: any,
  query: string,
  variables: any,
  jobId: string,
  maxRetries = 5,
  baseDelayMs = 1000
): Promise<any> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await admin.graphql(query, { variables });
      const responseJson = await response.json();

      // Check for GraphQL errors, specifically rate limit errors
      if (responseJson.errors && responseJson.errors.length > 0) {
        const rateLimitError = responseJson.errors.find((error: any) =>
          error.message.includes("Throttled") ||
          error.message.includes("rate limit") ||
          error.extensions?.code === "THROTTLED"
        );

        if (rateLimitError) {
          logToFile(jobId, "warn", `Rate limit hit (attempt ${attempt}/${maxRetries}). Retrying in ${baseDelayMs * Math.pow(2, attempt - 1)}ms...`);
          await delay(baseDelayMs * Math.pow(2, attempt - 1));
          continue; // Retry
        }
      }

      return responseJson; // Return successful response
    } catch (error: any) {
      logToFile(jobId, "error", `GraphQL request failed (attempt ${attempt}/${maxRetries}): ${error.message}`);
      if (attempt < maxRetries) {
        await delay(baseDelayMs * Math.pow(2, attempt - 1));
      } else {
        throw error; // Re-throw if max retries reached
      }
    }
  }
  throw new Error("Max retries exceeded for GraphQL request.");
}

async function processJob() {
  let job;
  try {
    job = await getNextPendingJob();

    if (job) {
      await updateJobStatus(job.id, "processing", undefined, new Date()); // Set startedAt
      logToFile(job.id, "info", `Worker ${WORKER_ID} picked up job ${job.id} (Type: ${job.type})`);

      const handler = jobHandlers[job.type as JobType];
      if (handler) {
        if (!job.shop) {
          logToFile(job.id, "error", "Job has no shop domain, cannot authenticate with Shopify Admin API.");
          await updateJobStatus(job.id, "failed", "Missing shop domain for authentication");
          return; // Exit early if shop domain is missing
        }
        try {
          // CORRECT USAGE for background jobs
          const { admin } = await unauthenticated.admin(job.shop);
          logToFile(job.id, "info", `Authenticated with Shopify Admin API for shop: ${job.shop}`);

          // Pass the authenticated admin client to the handler
          const success = await handler.handle(job, admin);

          if (success) {
            await updateJobStatus(job.id, "completed");
            logToFile(job.id, "info", `Job ${job.id} completed successfully.`);
          } else {
            const retryResult = await retryJob(job.id);
            if (retryResult) {
              logToFile(job.id, "info", `Job ${job.id} scheduled for retry (attempt ${retryResult.retryCount}/${retryResult.maxRetries})`);
            } else {
              logToFile(job.id, "error", `Job ${job.id} failed and max retries exceeded; marked as failed`);
            }
          }
        } catch (authError: unknown) {
          const authErrorMessage = authError instanceof Error ? authError.message : String(authError);
          logToFile(job.id, "error", `Authentication failed for job ID ${job.id} on shop ${job.shop}: ${authErrorMessage}`);
          await updateJobStatus(job.id, "failed", `Authentication failed: ${authErrorMessage}`);
        }
      } else {
        logToFile(job.id, "error", `No handler found for job type: ${job.type}`);
        await updateJobStatus(job.id, "failed", `No handler found for job type: ${job.type}`);
      }
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(job?.id || "unknown", "error", `Error processing job: ${errorMessage}`);
    if (job?.id) {
      await updateJobStatus(job.id, "failed", errorMessage);
    }
  } finally {
    if (job) {
      // Ensure the job isn't left in a processing state indefinitely.
      // This is a fallback. The job should be completed or failed within the try block.
    }
  }
}

export async function startWorker() {
  logToFile("worker-startup", "info", `Generic Worker ${WORKER_ID} started.`);
  setInterval(processJob, 5000); // Poll for new jobs every 5 seconds
}
