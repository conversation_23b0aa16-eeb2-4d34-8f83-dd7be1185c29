// 🦠 BACTERIAL VENDOR TAGGING UTILITIES
// Small, focused functions for vendor-based customer tagging

import { executeQuery } from './shopify';

/**
 * Vendor tagging configuration.
 */
export interface VendorTaggingConfig {
  // No specific configuration needed for now
}

/**
 * Order customer and line items data structure.
 */
export interface OrderVendorData {
  order: {
    id: string;
    customer: {
      id: string;
      tags: string[];
    } | null;
    lineItems: {
      edges: Array<{
        node: {
          vendor: string | null;
        };
      }>;
      pageInfo: {
        hasNextPage: boolean;
        endCursor: string | null;
      };
    };
  } | null;
}

/**
 * GraphQL queries for vendor tagging.
 */
export const GET_ORDER_AND_CUSTOMER_QUERY = `#graphql
  query getOrderAndCustomer($orderId: ID!) {
    order(id: $orderId) {
      id
      customer { id tags }
      lineItems(first: 250) {
        edges { node { vendor } }
        pageInfo { hasNextPage endCursor }
      }
    }
  }`;

export const GET_SUBSEQUENT_LINE_ITEMS_QUERY = `#graphql
  query getSubsequentLineItems($orderId: ID!, $after: String!) {
    order(id: $orderId) {
      lineItems(first: 250, after: $after) {
        edges { node { vendor } }
        pageInfo { hasNextPage, endCursor }
      }
    }
  }`;

/**
 * Extract vendors from line items.
 * Usage: const vendors = extractVendorsFromLineItems(lineItems.edges)
 */
export const extractVendorsFromLineItems = (
  lineItemEdges: Array<{ node: { vendor: string | null } }>
): string[] => {
  return lineItemEdges
    .map(edge => edge.node.vendor)
    .filter((vendor): vendor is string => Boolean(vendor));
};

/**
 * Get order and customer data with first page of line items.
 * Usage: const data = await getOrderAndCustomerData(admin, orderId)
 */
export const getOrderAndCustomerData = async (
  admin: any,
  orderId: string
): Promise<OrderVendorData | null> => {
  const response = await executeQuery(admin, GET_ORDER_AND_CUSTOMER_QUERY, { orderId });
  return response as OrderVendorData;
};

/**
 * Fetch all vendors from order line items with pagination.
 * Usage: const vendors = await fetchAllOrderVendors(admin, orderId, initialLineItems)
 */
export const fetchAllOrderVendors = async (
  admin: any,
  orderId: string,
  initialLineItems: any,
  onProgress?: (vendorCount: number) => void
): Promise<Set<string>> => {
  const allVendors = new Set<string>();
  
  // Process first page
  const firstPageVendors = extractVendorsFromLineItems(initialLineItems.edges);
  firstPageVendors.forEach(vendor => allVendors.add(vendor));
  
  let hasNextPage = initialLineItems.pageInfo.hasNextPage;
  let cursor = initialLineItems.pageInfo.endCursor;
  
  // Process subsequent pages
  while (hasNextPage && cursor) {
    const response = await executeQuery(admin, GET_SUBSEQUENT_LINE_ITEMS_QUERY, {
      orderId,
      after: cursor
    });
    
    const nextLineItems = response.order?.lineItems;
    if (!nextLineItems) {
      break;
    }
    
    const pageVendors = extractVendorsFromLineItems(nextLineItems.edges);
    pageVendors.forEach(vendor => allVendors.add(vendor));
    
    hasNextPage = nextLineItems.pageInfo.hasNextPage;
    cursor = nextLineItems.pageInfo.endCursor;
    
    onProgress?.(allVendors.size);
  }
  
  return allVendors;
};

/**
 * Determine vendor tags to add to customer.
 * Usage: const tags = determineVendorTags(vendors, customerTags)
 */
export const determineVendorTags = (
  vendors: Set<string>,
  customerTags: string[]
): string[] => {
  const customerTagsSet = new Set(customerTags);
  return Array.from(vendors).filter(vendor => !customerTagsSet.has(vendor));
};

/**
 * Process vendor tagging for an order.
 * Usage: const result = await processVendorTagging(admin, orderId, config)
 */
export const processVendorTagging = async (
  admin: any,
  orderId: string,
  config: VendorTaggingConfig,
  onProgress?: (vendorCount: number) => void
): Promise<{
  success: boolean;
  customerId?: string;
  vendorTags?: string[];
  reason?: string;
}> => {
  // Get order and customer data
  const orderData = await getOrderAndCustomerData(admin, orderId);
  
  if (!orderData?.order) {
    return { success: true, reason: `Order ${orderId} not found` };
  }
  
  const { order } = orderData;
  
  if (!order.customer) {
    return { success: true, reason: `Order ${orderId} has no associated customer` };
  }
  
  const customerId = order.customer.id;
  const customerTags = order.customer.tags || [];
  
  // Fetch all vendors from line items
  const allVendors = await fetchAllOrderVendors(admin, orderId, order.lineItems, onProgress);
  
  if (allVendors.size === 0) {
    return { 
      success: true, 
      customerId,
      vendorTags: [],
      reason: `No vendors found in order ${orderId}` 
    };
  }
  
  // Determine tags to add
  const vendorTags = determineVendorTags(allVendors, customerTags);
  
  if (vendorTags.length === 0) {
    return { 
      success: true, 
      customerId,
      vendorTags: [],
      reason: `No new vendor tags to apply to customer ${customerId}` 
    };
  }
  
  return {
    success: true,
    customerId,
    vendorTags
  };
};

/**
 * Validate vendor tagging config.
 * Usage: const validation = validateVendorTaggingConfig(config)
 */
export const validateVendorTaggingConfig = (config: any): { isValid: boolean; error?: string } => {
  if (!config || typeof config !== 'object') {
    return { isValid: false, error: 'Config is required and must be an object' };
  }
  
  // Currently no specific validation needed for vendor tagging config
  return { isValid: true };
};

/**
 * Create vendor tagging context for logging.
 * Usage: const context = createVendorTaggingContext(orderId, customerId, vendors, tags)
 */
export const createVendorTaggingContext = (
  orderId: string,
  customerId?: string,
  vendorCount?: number,
  tagsToAdd?: string[]
): Record<string, any> => ({
  orderId,
  customerId,
  vendorCount,
  tagsToAdd,
  tagCount: tagsToAdd?.length || 0
});
