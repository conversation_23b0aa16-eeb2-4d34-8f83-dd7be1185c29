// 🦠 BACTERIAL SHOPIFY UTILITIES
// Small, focused Shopify-specific functions built on bacterial async utilities

import { 
  retryWithBackoff, 
  isRateLimitError, 
  extractGraphQLErrors, 
  hasGraphQLErrors,
  findErrorByType 
} from './async';

/**
 * Execute GraphQL request with automatic retry on rate limits.
 * Bacterial approach: composed from smaller async utilities.
 */
export const executeGraphQLWithRetry = async (
  admin: any,
  query: string,
  variables: any = {},
  options: {
    maxRetries?: number;
    baseDelayMs?: number;
    onRetry?: (attempt: number, error: Error) => void;
  } = {}
): Promise<any> => {
  const { maxRetries = 5, baseDelayMs = 1000, onRetry } = options;

  return retryWithBackoff(
    async () => {
      const response = await admin.graphql(query, { variables });
      const responseJson = await response.json();

      // Check for rate limit errors specifically
      if (hasGraphQLErrors(responseJson)) {
        const errors = extractGraphQLErrors(responseJson);
        const rateLimitError = findErrorByType(errors, isRateLimitError);
        
        if (rateLimitError) {
          throw new Error(`Rate limit: ${rateLimitError.message}`);
        }
        
        // For other GraphQL errors, don't retry
        throw new Error(`GraphQL error: ${errors[0]?.message || 'Unknown error'}`);
      }

      return responseJson;
    },
    maxRetries,
    baseDelayMs,
    onRetry
  );
};

/**
 * Check if shop domain is valid format.
 * Usage: isValidShopDomain('myshop.myshopify.com') → true
 */
export const isValidShopDomain = (shop: string): boolean => {
  if (!shop || typeof shop !== 'string') return false;
  return /^[a-zA-Z0-9][a-zA-Z0-9-]*\.myshopify\.com$/.test(shop);
};

/**
 * Extract shop name from domain.
 * Usage: extractShopName('myshop.myshopify.com') → 'myshop'
 */
export const extractShopName = (shopDomain: string): string => {
  if (!isValidShopDomain(shopDomain)) return '';
  return shopDomain.split('.')[0];
};

/**
 * Build shop domain from shop name.
 * Usage: buildShopDomain('myshop') → 'myshop.myshopify.com'
 */
export const buildShopDomain = (shopName: string): string => {
  if (!shopName) return '';
  return `${shopName}.myshopify.com`;
};

/**
 * Check if GraphQL response indicates success.
 * Usage: if (isSuccessfulResponse(response)) { ... }
 */
export const isSuccessfulResponse = (response: any): boolean => {
  return response && !hasGraphQLErrors(response) && response.data;
};

/**
 * Extract data from successful GraphQL response.
 * Usage: const data = extractResponseData(response)
 */
export const extractResponseData = (response: any): any => {
  if (!isSuccessfulResponse(response)) {
    throw new Error('Response is not successful or has no data');
  }
  return response.data;
};

/**
 * Create a GraphQL mutation wrapper with error handling.
 * Usage: const result = await executeMutation(admin, mutation, variables)
 */
export const executeMutation = async (
  admin: any,
  mutation: string,
  variables: any = {},
  options?: Parameters<typeof executeGraphQLWithRetry>[3]
): Promise<any> => {
  const response = await executeGraphQLWithRetry(admin, mutation, variables, options);
  return extractResponseData(response);
};

/**
 * Create a GraphQL query wrapper with error handling.
 * Usage: const result = await executeQuery(admin, query, variables)
 */
export const executeQuery = async (
  admin: any,
  query: string,
  variables: any = {},
  options?: Parameters<typeof executeGraphQLWithRetry>[3]
): Promise<any> => {
  const response = await executeGraphQLWithRetry(admin, query, variables, options);
  return extractResponseData(response);
};
