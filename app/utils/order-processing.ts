// 🦠 BACTERIAL ORDER PROCESSING UTILITIES
// Small, focused functions for different order processing strategies

import { getOrderTags, getOrderDetailsWithProducts } from './shopify-collections';

/**
 * Order processing strategy result.
 */
export interface OrderProcessingResult {
  existingTags: Set<string>;
  productIds: string[];
}

/**
 * Process order using enriched data (when productIds are provided).
 * Usage: const result = await processOrderWithEnrichedData(admin, orderId, productIds)
 */
export const processOrderWithEnrichedData = async (
  admin: any,
  orderId: string,
  productIds: string[]
): Promise<OrderProcessingResult> => {
  const tags = await getOrderTags(admin, orderId);
  
  return {
    existingTags: new Set(tags),
    productIds
  };
};

/**
 * Process order by fetching full details.
 * Usage: const result = await processOrderWithFullFetch(admin, orderId)
 */
export const processOrderWithFullFetch = async (
  admin: any,
  orderId: string
): Promise<OrderProcessingResult | null> => {
  const details = await getOrderDetailsWithProducts(admin, orderId);
  
  if (!details) {
    return null;
  }
  
  return {
    existingTags: new Set(details.tags),
    productIds: details.productIds
  };
};

/**
 * Process order using the appropriate strategy based on available data.
 * Usage: const result = await processOrderDetails(admin, orderId, jobData)
 */
export const processOrderDetails = async (
  admin: any,
  orderId: string,
  jobData: { productIds?: string[] },
  logger?: {
    info: (jobId: string, message: string) => void;
  },
  jobId?: string
): Promise<OrderProcessingResult | null> => {
  if (jobData.productIds && jobData.productIds.length > 0) {
    logger?.info(jobId || 'unknown', 'Using enriched productIds from job data');
    return processOrderWithEnrichedData(admin, orderId, jobData.productIds);
  }
  
  logger?.info(jobId || 'unknown', 'Fetching order details via GraphQL');
  return processOrderWithFullFetch(admin, orderId);
};

/**
 * Validate order processing result.
 * Usage: const validation = validateOrderResult(result, orderId)
 */
export const validateOrderResult = (
  result: OrderProcessingResult | null,
  orderId: string
): { isValid: boolean; reason?: string } => {
  if (!result) {
    return { isValid: false, reason: `Order ${orderId} not found` };
  }
  
  if (result.productIds.length === 0) {
    return { isValid: false, reason: `Order ${orderId} has no products` };
  }
  
  return { isValid: true };
};

/**
 * Create order processing context for logging.
 * Usage: const context = createOrderContext(orderId, strategy)
 */
export const createOrderContext = (
  orderId: string, 
  strategy: 'enriched' | 'full-fetch'
): Record<string, any> => ({
  orderId,
  strategy,
  timestamp: new Date().toISOString()
});

/**
 * Log order processing step.
 * Usage: logOrderStep(logger, jobId, 'info', 'Processing order', { orderId, productCount: 5 })
 */
export const logOrderStep = (
  logger: {
    info: (jobId: string, message: string) => void;
    error: (jobId: string, message: string) => void;
    warn: (jobId: string, message: string) => void;
  },
  jobId: string,
  level: 'info' | 'error' | 'warn',
  message: string,
  context?: Record<string, any>
) => {
  const contextStr = context ? ` ${JSON.stringify(context)}` : '';
  logger[level](jobId, `${message}${contextStr}`);
};
