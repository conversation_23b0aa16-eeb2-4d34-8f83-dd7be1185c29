// 🦠 BACTERIAL CART ATTRIBUTE UTILITIES
// Small, focused functions for cart attribute processing

/**
 * Cart attribute configuration.
 */
export interface CartAttributeTagConfig {
  cart_attribute_to_monitor: string;
}

/**
 * Lean order data from webhook.
 */
export interface LeanOrderData {
  admin_graphql_api_id: string;
  tags: string; // Comma-separated string from webhook
  note_attributes: { name: string; value: string }[];
}

/**
 * Normalized order data structure.
 */
export interface NormalizedOrderData {
  id: string;
  tags: string[];
  customAttributes: { key: string; value: string }[];
}

/**
 * Parse comma-separated tags string.
 * Usage: const tags = parseTagsString("tag1, tag2, tag3")
 */
export const parseTagsString = (tagsString: string): string[] =>
  tagsString
    .split(',')
    .map(tag => tag.trim())
    .filter(Boolean);

/**
 * Normalize lean order data to consistent structure.
 * Usage: const normalized = normalizeLeanOrderData(leanOrder)
 */
export const normalizeLeanOrderData = (leanOrder: LeanOrderData): NormalizedOrderData => ({
  id: leanOrder.admin_graphql_api_id,
  tags: parseTagsString(leanOrder.tags),
  customAttributes: leanOrder.note_attributes.map(attr => ({
    key: attr.name,
    value: attr.value,
  })),
});

/**
 * Find cart attribute by name.
 * Usage: const attribute = findCartAttribute(customAttributes, 'gift-message')
 */
export const findCartAttribute = (
  customAttributes: { key: string; value: string }[],
  attributeName: string
): { key: string; value: string } | undefined =>
  customAttributes.find(attr => attr.key === attributeName);

/**
 * Get cart attribute value.
 * Usage: const value = getCartAttributeValue(customAttributes, 'gift-message')
 */
export const getCartAttributeValue = (
  customAttributes: { key: string; value: string }[],
  attributeName: string
): string | null => {
  const attribute = findCartAttribute(customAttributes, attributeName);
  return attribute?.value?.trim() || null;
};

/**
 * Check if order already has tag.
 * Usage: if (orderHasTag(order.tags, 'gift-order')) { ... }
 */
export const orderHasTag = (tags: string[], tag: string): boolean =>
  tags.includes(tag);

/**
 * Process cart attribute tagging for an order.
 * Usage: const result = processCartAttributeTagging(orderData, config)
 */
export const processCartAttributeTagging = (
  orderData: NormalizedOrderData,
  config: CartAttributeTagConfig
): {
  shouldTag: boolean;
  tagValue?: string;
  reason?: string;
} => {
  const { cart_attribute_to_monitor } = config;
  const { id: orderId, tags, customAttributes } = orderData;

  // Find the target attribute
  const attributeValue = getCartAttributeValue(customAttributes, cart_attribute_to_monitor);

  if (!attributeValue) {
    return {
      shouldTag: false,
      reason: `Attribute "${cart_attribute_to_monitor}" not found on order ${orderId} or its value is empty`
    };
  }

  // Check if tag already exists
  if (orderHasTag(tags, attributeValue)) {
    return {
      shouldTag: false,
      reason: `Order ${orderId} is already tagged with "${attributeValue}"`
    };
  }

  return {
    shouldTag: true,
    tagValue: attributeValue
  };
};

/**
 * Validate cart attribute config.
 * Usage: const validation = validateCartAttributeConfig(config)
 */
export const validateCartAttributeConfig = (config: any): { isValid: boolean; error?: string } => {
  if (!config || typeof config !== 'object') {
    return { isValid: false, error: 'Config is required and must be an object' };
  }
  
  if (!config.cart_attribute_to_monitor || typeof config.cart_attribute_to_monitor !== 'string') {
    return { isValid: false, error: 'Config must have cart_attribute_to_monitor string' };
  }
  
  if (config.cart_attribute_to_monitor.trim().length === 0) {
    return { isValid: false, error: 'cart_attribute_to_monitor cannot be empty' };
  }
  
  return { isValid: true };
};

/**
 * Validate cart attribute job data.
 * Usage: const validation = validateCartAttributeJobData(jobData)
 */
export const validateCartAttributeJobData = (jobData: any): { isValid: boolean; error?: string } => {
  if (!jobData.order && !jobData.orderId) {
    return { isValid: false, error: 'Missing order or orderId' };
  }
  
  const configValidation = validateCartAttributeConfig(jobData.config);
  if (!configValidation.isValid) {
    return { isValid: false, error: configValidation.error };
  }
  
  return { isValid: true };
};

/**
 * Extract order data from job data (lean or ID-based).
 * Usage: const orderData = extractOrderData(jobData)
 */
export const extractOrderData = (jobData: any): NormalizedOrderData | null => {
  if (jobData.order) {
    return normalizeLeanOrderData(jobData.order);
  }
  
  // For ID-based processing, we'd need to fetch from Shopify
  // This handler currently only supports lean order data
  return null;
};

/**
 * Create cart attribute tagging context for logging.
 * Usage: const context = createCartAttributeContext(orderId, attributeName, value)
 */
export const createCartAttributeContext = (
  orderId: string,
  attributeName: string,
  attributeValue?: string
): Record<string, any> => ({
  orderId,
  attributeName,
  attributeValue,
  hasValue: Boolean(attributeValue)
});
