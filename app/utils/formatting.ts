import type { BadgeProps } from "@shopify/polaris";
import type { JobStatus, JobType } from "@prisma/client";
import { toTitleCase } from "./string";
import { getStatusSemantic, type SemanticStatus } from "./status";

/**
 * Shopify Polaris tone mapping from semantic status.
 * Bacterial approach: uses generic status utility + framework-specific mapping.
 */
const POLARIS_TONE_MAP: Record<SemanticStatus, BadgeProps['tone']> = {
  success: 'success',
  error: 'critical',
  info: 'info',
  warning: 'attention',
  neutral: undefined,
};

/**
 * Maps JobStatus to Polaris Badge Tone using bacterial status utilities.
 * @param status The job status.
 * @returns The corresponding badge tone or undefined for default.
 */
export const getStatusBadgeTone = (status: JobStatus): BadgeProps['tone'] => {
  const semantic = getStatusSemantic(status);
  return POLARIS_TONE_MAP[semantic];
};

/**
 * Formats the job type string for display using bacterial string utility.
 * @param type The job type.
 * @returns A human-readable job name.
 */
export const formatJobName = (type: JobType | string): string =>
    toTitleCase(type.toString());

import { RELATIVE_TIME_UNITS, toDate, dateDiff } from "./date";

/**
 * Create a relative time formatter (can be customized per locale).
 * Bacterial utility: small, focused, configurable.
 */
export const createRelativeTimeFormatter = (locale = "en", options: Intl.RelativeTimeFormatOptions = { numeric: "auto" }) =>
  new Intl.RelativeTimeFormat(locale, options);

/**
 * Format date as relative time string using bacterial date utilities.
 * @param dateInput Date to format
 * @param formatter Optional custom formatter
 */
export const formatRelativeTime = (
  dateInput: string | Date,
  formatter = createRelativeTimeFormatter()
): string => {
  const date = toDate(dateInput);
  const elapsed = dateDiff(date, new Date());

  for (const { unit, ms } of RELATIVE_TIME_UNITS) {
    if (Math.abs(elapsed) >= ms) {
      const value = Math.round(elapsed / ms);
      if (value === 0 && unit === "second") return "just now";
      return formatter.format(value, unit);
    }
  }
  return "just now";
};