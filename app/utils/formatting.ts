import type { BadgeProps } from "@shopify/polaris";
import type { JobStatus, JobType } from "@prisma/client";

/**
 * Maps JobStatus from Prisma to a valid Polaris Badge Tone.
 * @param status The job status.
 * @returns The corresponding badge tone or undefined for default.
 */
export const getStatusBadgeTone = (status: JobStatus): BadgeProps['tone'] => {
    switch (status) {
      case "completed":
        return "success";
      case "failed":
        return "critical";
      case "processing":
        return "info";
      case "pending":
        return "attention";
      case "retrying":
        return "warning";
      case "canceled":
        return undefined;
      default:
        return undefined;
    }
};

/**
 * Formats the job type string for display (e.g., "COLLECTION_VISIBILITY_UPDATE" to "Collection Visibility Update").
 * @param type The job type.
 * @returns A human-readable job name.
 */
export const formatJobName = (type: JobType | string): string => { // Allow string for flexibility if used elsewhere
    return type.toString().replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

const units: { unit: Intl.RelativeTimeFormatUnit; ms: number }[] = [
  { unit: "year", ms: 31536000000 },
  { unit: "month", ms: 2592000000 },
  { unit: "week", ms: 604800000 },
  { unit: "day", ms: 86400000 },
  { unit: "hour", ms: 3600000 },
  { unit: "minute", ms: 60000 },
  { unit: "second", ms: 1000 },
];

const rtf = new Intl.RelativeTimeFormat("en", { numeric: "auto" });

export const formatRelativeTime = (dateInput: string | Date): string => {
  const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
  const now = new Date();
  const elapsed = date.getTime() - now.getTime(); // Negative for past dates

  for (const { unit, ms } of units) {
    if (Math.abs(elapsed) >= ms) {
      const value = Math.round(elapsed / ms);
      if (value === 0 && unit === "second") return "just now";
      return rtf.format(value, unit);
    }
  }
  return "just now";
};