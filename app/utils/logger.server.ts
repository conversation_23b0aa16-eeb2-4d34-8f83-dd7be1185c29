import fs from "fs";
import path from "path";
import winston from "winston";
import "winston-daily-rotate-file"; // Necessary for DailyRotateFile transport

// Log directory for application logs
// Consider moving to path.join(process.cwd(), "logs") if you prefer logs outside the 'app' source directory,
// as 'app' usually contains source code rather than runtime-generated files.
const LOG_DIR = path.join(process.cwd(), "app", "logs");

// Ensure base log directory exists on startup.
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Winston loggers container to manage multiple logger instances (e.g., one per target file)
const loggers = new winston.Container();

// Default log filename as per Phase 5 plan for inventory updates.
const DEFAULT_ROTATING_LOG_FILENAME = "borring-stuffs.log";

/**
 * Retrieves or creates a Winston logger instance for the given filename
 * with daily rotation and other specified options.
 * @param baseLogFileName - The base name for the log file (e.g., "inventory-updates.log")
 */
function getOrCreateLogger(baseLogFileName: string): winston.Logger {
  if (!loggers.has(baseLogFileName)) {
    loggers.add(baseLogFileName, {
      format: winston.format.combine(
        winston.format.timestamp({ format: "YYYY-MM-DDTHH:mm:ss.SSSZ" }), // ISO 8601 timestamp
        winston.format.printf(({ timestamp, level, message, jobId }) => {
          // Custom format to match previous log structure and include jobId
          return `${timestamp} [Job: ${jobId || 'N/A'}] [${level.toUpperCase()}] ${message}`;
        })
      ),
      transports: [
        new winston.transports.DailyRotateFile({
          filename: path.join(LOG_DIR, baseLogFileName), // The active log file
          datePattern: "YYYY-MM-DD",    // Suffix for rotated files, e.g., inventory-updates.log.2023-10-27
          zippedArchive: true,          // Compress rotated files
          maxSize: "20m",               // Rotate if file size exceeds 20MB
          maxFiles: "14d",              // Keep logs for 14 days
          level: "info",                // Log 'info' level and above (warn, error)
          handleExceptions: true,       // Log unhandled exceptions to this file
          handleRejections: true,       // Log unhandled promise rejections
        }),
        // For development, you might want to add a console logger
        // ...(process.env.NODE_ENV !== 'production' ? [new winston.transports.Console({
        //   format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
        //   level: 'debug',
        // })] : []),
      ],
    });
  }
  return loggers.get(baseLogFileName);
}

/**
 * Logs a message to a specified rotating file within the LOG_DIR using Winston.
 * @param jobId - The ID of the job or context being processed.
 * @param level - Log level (Winston standard: "error", "warn", "info", "http", "verbose", "debug", "silly").
 * @param message - The message to log.
 * @param targetLogFilename - Optional: The filename for the log (e.g., "inventory.log", "orders.log").
 *                            Defaults to `DEFAULT_ROTATING_LOG_FILENAME`.
 */
export function logToFile(
  jobId: string,
  level: "error" | "warn" | "info" | "http" | "verbose" | "debug" | "silly",
  message: string,
  targetLogFilename: string = DEFAULT_ROTATING_LOG_FILENAME,
) {
  const logger = getOrCreateLogger(targetLogFilename);
  // Pass jobId as part of the metadata object for Winston
  logger.log(level, message, { jobId });
}