// 🦠 BACTERIAL BULK DATA UTILITIES
// Small, focused functions for processing bulk operation data

/**
 * Group bulk operation lines by typename.
 * Usage: const grouped = groupLinesByType(lines)
 */
export const groupLinesByType = <T extends { __typename?: string }>(lines: T[]) => {
  const groups = new Map<string, T[]>();
  
  lines.forEach(line => {
    const type = line.__typename || 'unknown';
    if (!groups.has(type)) {
      groups.set(type, []);
    }
    groups.get(type)!.push(line);
  });
  
  return groups;
};

/**
 * Create a lookup map from array of objects.
 * Usage: const map = createLookupMap(items, item => item.id, item => item.value)
 */
export const createLookupMap = <T, K, V>(
  items: T[],
  keyFn: (item: T) => K,
  valueFn: (item: T) => V
): Map<K, V> => {
  const map = new Map<K, V>();
  items.forEach(item => {
    const key = keyFn(item);
    const value = valueFn(item);
    if (key !== undefined && key !== null) {
      map.set(key, value);
    }
  });
  return map;
};

/**
 * Create a multi-value lookup map (one key -> multiple values).
 * Usage: const map = createMultiLookupMap(items, item => item.categoryId, item => item.id)
 */
export const createMultiLookupMap = <T, K, V>(
  items: T[],
  keyFn: (item: T) => K,
  valueFn: (item: T) => V
): Map<K, Set<V>> => {
  const map = new Map<K, Set<V>>();
  items.forEach(item => {
    const key = keyFn(item);
    const value = valueFn(item);
    if (key !== undefined && key !== null && value !== undefined && value !== null) {
      if (!map.has(key)) {
        map.set(key, new Set());
      }
      map.get(key)!.add(value);
    }
  });
  return map;
};

/**
 * Filter lines by typename.
 * Usage: const orders = filterByType(lines, 'Order')
 */
export const filterByType = <T extends { __typename?: string }>(
  lines: T[], 
  typename: string
): T[] => lines.filter(line => line.__typename === typename);

/**
 * Extract parent-child relationships from bulk data.
 * Usage: const relationships = extractParentChildMap(lines, 'LineItem')
 */
export const extractParentChildMap = <T extends { __typename?: string; __parentId?: string; id?: string }>(
  lines: T[],
  childTypename: string
): Map<string, string[]> => {
  const map = new Map<string, string[]>();
  
  lines
    .filter(line => line.__typename === childTypename && line.__parentId && line.id)
    .forEach(line => {
      const parentId = line.__parentId!;
      const childId = line.id!;
      
      if (!map.has(parentId)) {
        map.set(parentId, []);
      }
      map.get(parentId)!.push(childId);
    });
  
  return map;
};

/**
 * Merge multiple sets into one.
 * Usage: const combined = mergeSets([set1, set2, set3])
 */
export const mergeSets = <T>(sets: Set<T>[]): Set<T> => {
  const merged = new Set<T>();
  sets.forEach(set => {
    set.forEach(item => merged.add(item));
  });
  return merged;
};

/**
 * Convert Map to plain object for logging.
 * Usage: const obj = mapToObject(myMap)
 */
export const mapToObject = <K extends string | number, V>(map: Map<K, V>): Record<K, V> => {
  const obj = {} as Record<K, V>;
  map.forEach((value, key) => {
    obj[key] = value;
  });
  return obj;
};

/**
 * Convert Map of Sets to plain object for logging.
 * Usage: const obj = mapOfSetsToObject(myMapOfSets)
 */
export const mapOfSetsToObject = <K extends string | number, V>(
  map: Map<K, Set<V>>
): Record<K, V[]> => {
  const obj = {} as Record<K, V[]>;
  map.forEach((set, key) => {
    obj[key] = Array.from(set);
  });
  return obj;
};

/**
 * Count total items across all sets in a map.
 * Usage: const total = countItemsInMapOfSets(myMapOfSets)
 */
export const countItemsInMapOfSets = <K, V>(map: Map<K, Set<V>>): number => {
  let count = 0;
  map.forEach(set => {
    count += set.size;
  });
  return count;
};
