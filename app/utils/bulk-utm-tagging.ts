// 🦠 BACTERIAL BULK UTM TAGGING UTILITIES
// Small, focused functions for bulk UTM-based order tagging



/**
 * Bulk UTM tagging configuration.
 * Should match the single event UTM config - no prefix needed.
 */
export interface BulkUtmTaggingConfig {
  // No prefix - bulk operations should work exactly like single events
  // Just process many orders with the same logic
}

/**
 * Extract UTM parameters from moment.
 * Usage: const utmParams = extractUtmFromMoment(moment)
 */
export const extractUtmFromMoment = (moment: any): Record<string, string> | null => {
  if (!moment.utmParameters || typeof moment.utmParameters !== 'object') {
    return null;
  }
  
  return moment.utmParameters;
};

/**
 * Convert UTM parameters to raw value tags (no prefixes).
 * Usage: const tags = convertUtmParamsToTags(utmParams)
 * Matches original Liquid: {% assign tags_to_add[tags_to_add.size] = parameter_set[parameter_name] %}
 */
export const convertUtmParamsToTags = (
  utmParams: Record<string, string>
): string[] => {
  return Object.entries(utmParams)
    .filter(([, value]) => value && typeof value === 'string')
    .map(([, value]) => value);  // ✅ FIXED: Only return raw value, no prefix
};

/**
 * Process moment for UTM tagging.
 * Usage: const result = processMomentForUtm(moment)
 */
export const processMomentForUtm = (
  moment: any
): { orderId: string; utmTags: string[] } | null => {
  if (!moment.__parentId) {
    return null;
  }

  const utmParams = extractUtmFromMoment(moment);
  if (!utmParams) {
    return null;
  }

  const utmTags = convertUtmParamsToTags(utmParams);
  if (utmTags.length === 0) {
    return null;
  }

  return {
    orderId: moment.__parentId,
    utmTags
  };
};

/**
 * Extract order-UTM relationships from bulk data.
 * Usage: const relationships = extractOrderUtmRelationships(lines)
 */
export const extractOrderUtmRelationships = (
  lines: any[]
): Map<string, Set<string>> => {
  const orderTags = new Map<string, Set<string>>();

  // Filter for moments with UTM parameters
  const moments = lines.filter(line =>
    line.__parentId &&
    line.utmParameters &&
    typeof line.utmParameters === 'object'
  );

  // Process each moment for UTM tags
  moments.forEach(moment => {
    const utmResult = processMomentForUtm(moment);
    if (!utmResult) return;

    const { orderId, utmTags } = utmResult;

    if (!orderTags.has(orderId)) {
      orderTags.set(orderId, new Set());
    }

    utmTags.forEach(tag => {
      orderTags.get(orderId)!.add(tag);
    });
  });

  return orderTags;
};

/**
 * Validate bulk UTM tagging config.
 * Usage: const validation = validateBulkUtmConfig(config)
 */
export const validateBulkUtmConfig = (config: any): { isValid: boolean; error?: string } => {
  if (!config || typeof config !== 'object') {
    return { isValid: false, error: 'Config is required and must be an object' };
  }

  // No specific validation needed - bulk operations work like single events
  return { isValid: true };
};

/**
 * Create bulk UTM tagging context for logging.
 * Usage: const context = createBulkUtmContext(shop, lineCount, orderCount)
 */
export const createBulkUtmContext = (
  shop: string,
  lineCount: number,
  orderCount: number
): Record<string, any> => ({
  shop,
  lineCount,
  orderCount,
  operation: 'bulk-utm-tagging'
});

/**
 * Process bulk UTM tagging data.
 * Usage: const orderTags = processBulkUtmData(lines)
 */
export const processBulkUtmData = (
  lines: any[]
): Map<string, Set<string>> => {
  return extractOrderUtmRelationships(lines);
};

/**
 * Count total UTM tags to be applied.
 * Usage: const total = countUtmTags(orderTagsMap)
 */
export const countUtmTags = (orderTags: Map<string, Set<string>>): number => {
  let total = 0;
  orderTags.forEach(tags => {
    total += tags.size;
  });
  return total;
};

/**
 * Get unique UTM parameters from moments.
 * Usage: const utmParams = getUniqueUtmParameters(moments)
 */
export const getUniqueUtmParameters = (moments: any[]): Map<string, Set<string>> => {
  const utmMap = new Map<string, Set<string>>();
  
  moments.forEach(moment => {
    const utmParams = extractUtmFromMoment(moment);
    if (utmParams) {
      Object.entries(utmParams).forEach(([key, value]) => {
        if (key && value) {
          if (!utmMap.has(key)) {
            utmMap.set(key, new Set());
          }
          utmMap.get(key)!.add(value);
        }
      });
    }
  });
  
  return utmMap;
};

/**
 * Filter moments with UTM parameters.
 * Usage: const utmMoments = filterMomentsWithUtm(lines)
 */
export const filterMomentsWithUtm = (lines: any[]): any[] =>
  lines.filter(line => 
    line.__parentId && 
    line.utmParameters &&
    typeof line.utmParameters === 'object' &&
    Object.keys(line.utmParameters).length > 0
  );
