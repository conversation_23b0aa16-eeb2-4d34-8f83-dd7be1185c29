// 🦠 BACTERIAL INVENTORY UTILITIES
// Small, focused functions for inventory management

import { executeQuery, executeMutation } from './shopify';

/**
 * Inventory item data structure.
 */
export interface InventoryItemData {
  id: string;
  variant: {
    id: string;
    inventoryQuantity: number;
    product: {
      id: string;
      status: string;
      resourcePublications: {
        edges: Array<{
          node: {
            publication: { id: string; name: string };
            isPublished: boolean;
          };
        }>;
      };
      collections: {
        edges: Array<{
          node: {
            id: string;
            title: string;
            resourcePublications: {
              edges: Array<{
                node: {
                  publication: { id: string; name: string };
                  isPublished: boolean;
                };
              }>;
            };
          };
        }>;
        pageInfo: {
          hasNextPage: boolean;
          endCursor: string | null;
        };
      };
    };
  };
}

/**
 * GraphQL query for inventory item with product and collections.
 */
export const GET_INVENTORY_ITEM_QUERY = `#graphql
  query inventoryItemProductAndCollections($id: ID!) {
    inventoryItem(id: $id) {
      id
      variant {
        id
        inventoryQuantity
        product {
          id
          status
          resourcePublications(first: 250) {
            edges {
              node {
                publication {
                  id
                  name
                }
                isPublished
              }
            }
          }
          collections(first: 250) {
            edges {
              node {
                id
                title
                resourcePublications(first: 250) {
                  edges {
                    node {
                      publication {
                        id
                        name
                      }
                      isPublished
                    }
                  }
                }
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      }
    }
  }`;

/**
 * Check if product is in stock.
 * Usage: if (isProductInStock(inventoryQuantity)) { ... }
 */
export const isProductInStock = (inventoryQuantity: number): boolean =>
  inventoryQuantity > 0;

/**
 * Check if product is published.
 * Usage: if (isProductPublished(product)) { ... }
 */
export const isProductPublished = (product: any): boolean =>
  product.status === "ACTIVE" && 
  product.resourcePublications.edges.some((edge: any) => edge.node.isPublished);

/**
 * Extract publication IDs from product.
 * Usage: const pubIds = extractPublicationIds(product)
 */
export const extractPublicationIds = (product: any): string[] =>
  product.resourcePublications.edges.map((edge: any) => edge.node.publication.id);

/**
 * Check if product is published in specific channel.
 * Usage: if (isProductPublishedInChannel(product, publicationId)) { ... }
 */
export const isProductPublishedInChannel = (product: any, publicationId: string): boolean =>
  product.resourcePublications.edges.some(
    (edge: any) => edge.node.publication.id === publicationId && edge.node.isPublished
  );

/**
 * Get inventory item data.
 * Usage: const data = await getInventoryItemData(admin, inventoryItemId)
 */
export const getInventoryItemData = async (
  admin: any,
  inventoryItemId: string
): Promise<InventoryItemData | null> => {
  const response = await executeQuery(admin, GET_INVENTORY_ITEM_QUERY, {
    id: `gid://shopify/InventoryItem/${inventoryItemId}`
  });
  
  return response.inventoryItem || null;
};

/**
 * Validate inventory item data.
 * Usage: const validation = validateInventoryItemData(data)
 */
export const validateInventoryItemData = (
  data: InventoryItemData | null,
  inventoryItemId: string
): { isValid: boolean; reason?: string } => {
  if (!data || !data.variant || !data.variant.product) {
    return {
      isValid: false,
      reason: `Inventory item with ID ${inventoryItemId} or its associated product/variant not found`
    };
  }
  
  return { isValid: true };
};

/**
 * Create inventory processing context.
 * Usage: const context = createInventoryContext(inventoryItemId, product, inventoryQuantity)
 */
export const createInventoryContext = (
  inventoryItemId: string,
  product: any,
  inventoryQuantity: number
): Record<string, any> => ({
  inventoryItemId,
  productId: product.id,
  inventoryQuantity,
  inStock: isProductInStock(inventoryQuantity),
  published: isProductPublished(product)
});
