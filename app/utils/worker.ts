// 🦠 BACTERIAL WORKER UTILITIES
// Small, focused worker-related functions

import { randomString } from './string';

/**
 * Generate a unique worker ID.
 * Usage: const workerId = generateWorkerId('generic-worker')
 */
export const generateWorkerId = (prefix: string = 'worker'): string =>
  `${prefix}-${randomString(7)}`;

/**
 * Create a job handler registry.
 * Usage: const registry = createJobHandlerRegistry()
 */
export const createJobHandlerRegistry = <T extends string>() => {
  const handlers = new Map<T, { handle: (job: any, admin: any) => Promise<boolean> }>();

  return {
    register: (jobType: T, handler: { handle: (job: any, admin: any) => Promise<boolean> }) => {
      handlers.set(jobType, handler);
    },
    
    get: (jobType: T) => handlers.get(jobType),
    
    has: (jobType: T) => handlers.has(jobType),
    
    getAll: () => Array.from(handlers.keys()),
    
    size: () => handlers.size
  };
};

/**
 * Check if job has required fields.
 * Usage: if (isValidJob(job)) { ... }
 */
export const isValidJob = (job: any): boolean => {
  return job && 
         typeof job.id === 'string' && 
         typeof job.type === 'string' &&
         typeof job.shop === 'string';
};

/**
 * Extract error message safely from unknown error.
 * Usage: const message = extractErrorMessage(error)
 */
export const extractErrorMessage = (error: unknown): string => {
  if (error instanceof Error) return error.message;
  if (typeof error === 'string') return error;
  return 'Unknown error occurred';
};

/**
 * Create a polling interval manager.
 * Usage: const poller = createPoller(() => processJob(), 5000)
 */
export const createPoller = (
  fn: () => Promise<void> | void,
  intervalMs: number
) => {
  let intervalId: NodeJS.Timeout | null = null;
  let isRunning = false;

  return {
    start: () => {
      if (isRunning) return;
      isRunning = true;
      intervalId = setInterval(async () => {
        try {
          await fn();
        } catch (error) {
          console.error('Poller error:', extractErrorMessage(error));
        }
      }, intervalMs);
    },
    
    stop: () => {
      if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
      }
      isRunning = false;
    },
    
    isRunning: () => isRunning
  };
};

/**
 * Create a job processor with error handling.
 * Usage: const processor = createJobProcessor(handlers, logger)
 */
export const createJobProcessor = <T extends string>(
  handlerRegistry: ReturnType<typeof createJobHandlerRegistry<T>>,
  logger: {
    info: (jobId: string, message: string) => void;
    error: (jobId: string, message: string) => void;
    warn: (jobId: string, message: string) => void;
  }
) => {
  return async (
    job: any,
    authenticate: (shop: string) => Promise<{ admin: any }>,
    updateStatus: (jobId: string, status: string, error?: string, startedAt?: Date) => Promise<void>,
    retryJob: (jobId: string) => Promise<any>
  ) => {
    if (!isValidJob(job)) {
      logger.error(job?.id || 'unknown', 'Invalid job structure');
      return;
    }

    try {
      await updateStatus(job.id, "processing", undefined, new Date());
      logger.info(job.id, `Processing job ${job.id} (Type: ${job.type})`);

      const handler = handlerRegistry.get(job.type as T);
      if (!handler) {
        const errorMsg = `No handler found for job type: ${job.type}`;
        logger.error(job.id, errorMsg);
        await updateStatus(job.id, "failed", errorMsg);
        return;
      }

      try {
        const { admin } = await authenticate(job.shop);
        logger.info(job.id, `Authenticated with Shopify Admin API for shop: ${job.shop}`);

        const success = await handler.handle(job, admin);

        if (success) {
          await updateStatus(job.id, "completed");
          logger.info(job.id, `Job ${job.id} completed successfully.`);
        } else {
          const retryResult = await retryJob(job.id);
          if (retryResult) {
            logger.info(job.id, `Job ${job.id} scheduled for retry (attempt ${retryResult.retryCount}/${retryResult.maxRetries})`);
          } else {
            logger.error(job.id, `Job ${job.id} failed and max retries exceeded; marked as failed`);
          }
        }
      } catch (authError) {
        const authErrorMessage = extractErrorMessage(authError);
        logger.error(job.id, `Authentication failed for job ID ${job.id} on shop ${job.shop}: ${authErrorMessage}`);
        await updateStatus(job.id, "failed", `Authentication failed: ${authErrorMessage}`);
      }
    } catch (error) {
      const errorMessage = extractErrorMessage(error);
      logger.error(job.id, `Error processing job: ${errorMessage}`);
      await updateStatus(job.id, "failed", errorMessage);
    }
  };
};
