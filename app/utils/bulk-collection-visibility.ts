// 🦠 BACTERIAL BULK COLLECTION VISIBILITY UTILITIES
// Small, focused functions for bulk collection visibility management

import { executeQuery, executeMutation } from './shopify';

/**
 * GraphQL queries for bulk collection visibility.
 */
export const GET_ALL_PUBLICATIONS_QUERY = `#graphql
  query getAllPublications($first: Int!, $after: String) {
    publications(first: $first, after: $after) {
      pageInfo { hasNextPage endCursor }
      edges {
        node {
          id
          name
        }
      }
    }
  }`;

export const GET_ALL_COLLECTIONS_QUERY = `#graphql
  query getAllCollections($first: Int!, $after: String) {
    collections(first: $first, after: $after) {
      pageInfo { hasNextPage endCursor }
      edges {
        node {
          id
          title
          resourcePublications(first: 50) {
            edges {
              node {
                isPublished
                publication { id name }
              }
            }
          }
        }
      }
    }
  }`;

export const GET_COLLECTION_PRODUCTS_QUERY = `#graphql
  query collectionProductsWithPublications($id: ID!, $first: Int!, $after: String) {
    collection(id: $id) {
      products(first: $first, after: $after) {
        pageInfo { hasNextPage endCursor }
        edges {
          node {
            status
            totalInventory
            resourcePublications(first: 50) {
              edges {
                node {
                  isPublished
                  publication { id }
                }
              }
            }
          }
        }
      }
    }
  }`;

/**
 * Fetch all publications with pagination.
 * Usage: const publications = await fetchAllPublications(admin, onProgress)
 */
export const fetchAllPublications = async (
  admin: any,
  onProgress?: (count: number) => void
): Promise<any[]> => {
  let allPublications: any[] = [];
  let cursor: string | null = null;
  let hasNextPage = true;

  while (hasNextPage) {
    const response = await executeQuery(admin, GET_ALL_PUBLICATIONS_QUERY, {
      first: 100,
      after: cursor
    });

    const pubsData = response.publications;
    if (pubsData?.edges) {
      allPublications = allPublications.concat(pubsData.edges.map((edge: any) => edge.node));
      onProgress?.(allPublications.length);
    }
    hasNextPage = pubsData?.pageInfo?.hasNextPage || false;
    cursor = pubsData?.pageInfo?.endCursor || null;
  }

  return allPublications;
};

/**
 * Fetch all collections with pagination.
 * Usage: const collections = await fetchAllCollections(admin, onProgress)
 */
export const fetchAllCollections = async (
  admin: any,
  onProgress?: (count: number) => void
): Promise<any[]> => {
  let allCollections: any[] = [];
  let cursor: string | null = null;
  let hasNextPage = true;

  while (hasNextPage) {
    const response = await executeQuery(admin, GET_ALL_COLLECTIONS_QUERY, {
      first: 100,
      after: cursor
    });

    const collectionsData = response.collections;
    if (collectionsData?.edges) {
      allCollections = allCollections.concat(collectionsData.edges.map((edge: any) => edge.node));
      onProgress?.(allCollections.length);
    }
    hasNextPage = collectionsData?.pageInfo?.hasNextPage || false;
    cursor = collectionsData?.pageInfo?.endCursor || null;
  }

  return allCollections;
};

/**
 * Determine publishable channels for a collection based on product stock.
 * Usage: const channels = await getCollectionPublishableChannels(admin, collectionId)
 */
export const getCollectionPublishableChannels = async (
  admin: any,
  collectionId: string
): Promise<Map<string, boolean>> => {
  const publishableChannels = new Map<string, boolean>();
  let cursor: string | null = null;
  let hasNextPage = true;

  while (hasNextPage) {
    const response = await executeQuery(admin, GET_COLLECTION_PRODUCTS_QUERY, {
      id: collectionId,
      first: 100,
      after: cursor
    });

    const products = response.collection?.products?.edges || [];

    for (const productEdge of products) {
      const product = productEdge.node;

      // Only consider active, in-stock products
      if (product.status !== "ACTIVE" || product.totalInventory <= 0) {
        continue;
      }

      // This product is in stock. Mark all its published channels as "live".
      for (const pubEdge of product.resourcePublications.edges) {
        if (pubEdge.node.isPublished) {
          publishableChannels.set(pubEdge.node.publication.id, true);
        }
      }
    }

    hasNextPage = response.collection?.products?.pageInfo?.hasNextPage || false;
    cursor = response.collection?.products?.pageInfo?.endCursor || null;
  }

  return publishableChannels;
};

/**
 * Create current publication status map for a collection.
 * Usage: const statusMap = createCurrentPublicationStatusMap(collection)
 */
export const createCurrentPublicationStatusMap = (collection: any): Map<string, boolean> => {
  const statusMap = new Map<string, boolean>();
  
  for (const pubEdge of collection.resourcePublications.edges) {
    statusMap.set(pubEdge.node.publication.id, pubEdge.node.isPublished);
  }
  
  return statusMap;
};

/**
 * Determine publication action needed.
 * Usage: const action = determinePublicationAction(shouldBePublished, isCurrentlyPublished)
 */
export const determinePublicationAction = (
  shouldBePublished: boolean,
  isCurrentlyPublished: boolean
): 'publish' | 'unpublish' | 'none' => {
  if (shouldBePublished && !isCurrentlyPublished) {
    return 'publish';
  }
  if (!shouldBePublished && isCurrentlyPublished) {
    return 'unpublish';
  }
  return 'none';
};

/**
 * Execute publication action for a collection.
 * Usage: await executePublicationAction(admin, action, collectionId, publicationId)
 */
export const executePublicationAction = async (
  admin: any,
  action: 'publish' | 'unpublish',
  collectionId: string,
  publicationId: string
): Promise<{ success: boolean; errors: any[] }> => {
  const mutation = action === 'publish' 
    ? `#graphql
        mutation PublishablePublish($id: ID!, $publicationId: ID!) {
          publishablePublish(id: $id, input: {publicationId: $publicationId}) {
            publishable {
              publishedOnPublication(publicationId: $publicationId)
            }
            userErrors {
              field
              message
            }
          }
        }`
    : `#graphql
        mutation PublishableUnpublish($id: ID!, $publicationId: ID!) {
          publishableUnpublish(id: $id, input: {publicationId: $publicationId}) {
            publishable {
              publishedOnPublication(publicationId: $publicationId)
            }
            userErrors {
              field
              message
            }
          }
        }`;

  const response = await executeMutation(admin, mutation, {
    id: collectionId,
    publicationId
  });

  const result = action === 'publish' ? response.publishablePublish : response.publishableUnpublish;
  const errors = result?.userErrors || [];

  return {
    success: errors.length === 0,
    errors
  };
};

/**
 * Process collection visibility for a single collection.
 * Usage: await processCollectionVisibility(admin, collection, publications, logger, jobId)
 */
export const processCollectionVisibility = async (
  admin: any,
  collection: any,
  publications: any[],
  logger: {
    info: (jobId: string, message: string) => void;
    error: (jobId: string, message: string) => void;
  },
  jobId: string
): Promise<void> => {
  logger.info(jobId, `Processing collection: "${collection.title}" (ID: ${collection.id})`);

  // Determine which channels should have this collection published
  const publishableChannels = await getCollectionPublishableChannels(admin, collection.id);
  logger.info(jobId, `Found ${publishableChannels.size} publishable channels for "${collection.title}"`);

  // Get current publication status
  const currentStatus = createCurrentPublicationStatusMap(collection);

  // Process each publication
  for (const publication of publications) {
    const publicationId = publication.id;
    const publicationName = publication.name;
    
    const shouldBePublished = publishableChannels.get(publicationId) || false;
    const isCurrentlyPublished = currentStatus.get(publicationId) || false;
    
    const action = determinePublicationAction(shouldBePublished, isCurrentlyPublished);
    
    if (action !== 'none') {
      logger.info(jobId, `Collection "${collection.title}" on channel "${publicationName}": ${action}ing (should: ${shouldBePublished}, current: ${isCurrentlyPublished})`);
      
      const result = await executePublicationAction(admin, action, collection.id, publicationId);
      
      if (!result.success) {
        logger.error(jobId, `Failed to ${action} collection "${collection.title}" on "${publicationName}": ${JSON.stringify(result.errors)}`);
      }
    }
  }
};
