const DOTS = '...';

/**
 * Helper function to generate a range of numbers.
 * @param start The start of the range.
 * @param end The end of the range.
 * @returns Array of numbers in the range.
 */
const range = (start: number, end: number): number[] => {
  const length = end - start + 1;
  return Array.from({ length }, (_, idx) => idx + start);
};

/**
 * Generates a pagination range with ellipses.
 * @param currentPage The current active page.
 * @param totalPages The total number of pages.
 * @param pageNeighbours Number of page numbers to show on each side of the current page.
 * @returns Array of page numbers and DOTS string for ellipses.
 */
export const generatePaginationRange = (
  currentPage: number,
  totalPages: number,
  pageNeighbours: number = 1
): (number | typeof DOTS)[] => {
  const totalPageNumbers = pageNeighbours * 2 + 3; // pageNeighbours + firstPage + lastPage + currentPage + 2*DOTS
  const totalBlocks = totalPageNumbers + 2; // Including DOTS

  if (totalPages <= totalBlocks) {
    return range(1, totalPages);
  }

  const leftSiblingIndex = Math.max(currentPage - pageNeighbours, 1);
  const rightSiblingIndex = Math.min(currentPage + pageNeighbours, totalPages);

  const shouldShowLeftDots = leftSiblingIndex > 2;
  const shouldShowRightDots = rightSiblingIndex < totalPages - 1;

  const firstPageIndex = 1;
  const lastPageIndex = totalPages;

  if (!shouldShowLeftDots && shouldShowRightDots) {
    const leftItemCount = 3 + 2 * pageNeighbours;
    const leftRange = range(1, leftItemCount);
    return [...leftRange, DOTS, totalPages];
  }

  if (shouldShowLeftDots && !shouldShowRightDots) {
    const rightItemCount = 3 + 2 * pageNeighbours;
    const rightRange = range(totalPages - rightItemCount + 1, totalPages);
    return [firstPageIndex, DOTS, ...rightRange];
  }

  if (shouldShowLeftDots && shouldShowRightDots) {
    const middleRange = range(leftSiblingIndex, rightSiblingIndex);
    return [firstPageIndex, DOTS, ...middleRange, DOTS, lastPageIndex];
  }

  // This case should ideally not be reached if logic above is correct,
  // but as a fallback, return a simple range.
  return range(1, totalPages);
};