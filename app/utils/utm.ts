// 🦠 BACTERIAL UTM UTILITIES
// Small, focused functions for UTM parameter processing

/**
 * UTM parameter types.
 */
export interface UtmParameters {
  campaign?: string;
  source?: string;
  medium?: string;
  content?: string;
  term?: string;
}

/**
 * UTM tagging configuration.
 */
export interface UtmTaggingConfig {
  tag_with_utm_campaign: boolean;
  tag_with_utm_source: boolean;
  tag_with_utm_medium: boolean;
  tag_with_utm_content: boolean;
  tag_with_utm_term: boolean;
}

/**
 * Extract UTM parameters from customer journey moments.
 * Usage: const utmParams = extractUtmFromMoments(moments)
 */
export const extractUtmFromMoments = (moments: any[]): UtmParameters[] => {
  return moments
    .map(edge => edge.node?.utmParameters)
    .filter(Boolean);
};

/**
 * Get all unique UTM values from parameters array.
 * Usage: const values = getUniqueUtmValues(utmParams, 'campaign')
 */
export const getUniqueUtmValues = (
  utmParams: UtmParameters[], 
  field: keyof UtmParameters
): string[] => {
  const values = new Set<string>();
  
  utmParams.forEach(params => {
    const value = params[field];
    if (value && typeof value === 'string' && value.trim()) {
      values.add(value.trim());
    }
  });
  
  return Array.from(values);
};

/**
 * Check if UTM field should be tagged based on config.
 * Usage: if (shouldTagUtmField(config, 'campaign')) { ... }
 */
export const shouldTagUtmField = (
  config: UtmTaggingConfig, 
  field: keyof UtmParameters
): boolean => {
  const configMap: Record<keyof UtmParameters, keyof UtmTaggingConfig> = {
    campaign: 'tag_with_utm_campaign',
    source: 'tag_with_utm_source',
    medium: 'tag_with_utm_medium',
    content: 'tag_with_utm_content',
    term: 'tag_with_utm_term',
  };
  
  return config[configMap[field]] || false;
};

/**
 * Determine UTM tags to add based on config and existing tags.
 * Usage: const tags = determineUtmTags(utmParams, config, existingTags)
 */
export const determineUtmTags = (
  utmParams: UtmParameters[],
  config: UtmTaggingConfig,
  existingTags: string[] = []
): string[] => {
  const existingTagsSet = new Set(existingTags);
  const tagsToAdd = new Set<string>();
  
  const utmFields: (keyof UtmParameters)[] = ['campaign', 'source', 'medium', 'content', 'term'];
  
  utmFields.forEach(field => {
    if (shouldTagUtmField(config, field)) {
      const values = getUniqueUtmValues(utmParams, field);
      values.forEach(value => {
        if (!existingTagsSet.has(value)) {
          tagsToAdd.add(value);
        }
      });
    }
  });
  
  return Array.from(tagsToAdd);
};

/**
 * Extract UTM tags from order data.
 * Usage: const tags = extractUtmTagsFromOrder(orderData, config)
 */
export const extractUtmTagsFromOrder = (
  orderData: any,
  config: UtmTaggingConfig
): string[] => {
  const currentTags = orderData.tags || [];
  const moments = orderData.customerJourneySummary?.moments?.edges || [];
  
  const utmParams = extractUtmFromMoments(moments);
  return determineUtmTags(utmParams, config, currentTags);
};

/**
 * Validate UTM tagging config.
 * Usage: const validation = validateUtmConfig(config)
 */
export const validateUtmConfig = (config: any): { isValid: boolean; error?: string } => {
  if (!config || typeof config !== 'object') {
    return { isValid: false, error: 'Config is required and must be an object' };
  }
  
  const requiredFields: (keyof UtmTaggingConfig)[] = [
    'tag_with_utm_campaign',
    'tag_with_utm_source', 
    'tag_with_utm_medium',
    'tag_with_utm_content',
    'tag_with_utm_term'
  ];
  
  for (const field of requiredFields) {
    if (typeof config[field] !== 'boolean') {
      return { isValid: false, error: `Config field '${field}' must be a boolean` };
    }
  }
  
  return { isValid: true };
};

/**
 * Check if any UTM tagging is enabled.
 * Usage: if (isUtmTaggingEnabled(config)) { ... }
 */
export const isUtmTaggingEnabled = (config: UtmTaggingConfig): boolean => {
  return config.tag_with_utm_campaign ||
         config.tag_with_utm_source ||
         config.tag_with_utm_medium ||
         config.tag_with_utm_content ||
         config.tag_with_utm_term;
};

/**
 * Get enabled UTM fields from config.
 * Usage: const fields = getEnabledUtmFields(config)
 */
export const getEnabledUtmFields = (config: UtmTaggingConfig): (keyof UtmParameters)[] => {
  const fields: (keyof UtmParameters)[] = [];
  
  if (config.tag_with_utm_campaign) fields.push('campaign');
  if (config.tag_with_utm_source) fields.push('source');
  if (config.tag_with_utm_medium) fields.push('medium');
  if (config.tag_with_utm_content) fields.push('content');
  if (config.tag_with_utm_term) fields.push('term');
  
  return fields;
};
