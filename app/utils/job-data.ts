// 🦠 BACTERIAL JOB DATA UTILITIES
// Small, focused functions for job data parsing and validation

import { validateShape } from './validation';

/**
 * Safely parse JSON string.
 * Usage: const data = safeJsonParse(jsonString)
 */
export const safeJsonParse = <T = any>(jsonString: string): T | null => {
  try {
    return JSON.parse(jsonString);
  } catch {
    return null;
  }
};

/**
 * Parse job data with validation.
 * Usage: const result = parseJobData(job.data, { orderId: 'string', config: 'object' })
 */
export const parseJobData = <T = any>(
  dataString: string, 
  expectedShape?: Record<string, 'string' | 'number' | 'boolean' | 'object' | 'array'>
): { data: T | null; isValid: boolean; error: string | null } => {
  const parsed = safeJsonParse<T>(dataString);
  
  if (!parsed) {
    return { data: null, isValid: false, error: 'Invalid JSON data' };
  }

  if (expectedShape && !validateShape(parsed, expectedShape)) {
    return { data: null, isValid: false, error: 'Data does not match expected shape' };
  }

  return { data: parsed, isValid: true, error: null };
};

/**
 * Extract required fields from job data.
 * Usage: const result = extractRequiredFields(data, ['orderId', 'config'])
 */
export const extractRequiredFields = <T extends Record<string, any>>(
  data: T, 
  requiredFields: (keyof T)[]
): { isValid: boolean; error: string | null; fields: Partial<T> } => {
  const fields: Partial<T> = {};
  
  for (const field of requiredFields) {
    if (data[field] === undefined || data[field] === null) {
      return {
        isValid: false,
        error: `Missing required field: ${String(field)}`,
        fields: {}
      };
    }
    fields[field] = data[field];
  }

  return { isValid: true, error: null, fields };
};

/**
 * Create job data validator for specific structure.
 * Usage: const validator = createJobDataValidator(['orderId', 'config'])
 */
export const createJobDataValidator = <T extends Record<string, any>>(
  requiredFields: (keyof T)[],
  expectedShape?: Record<string, 'string' | 'number' | 'boolean' | 'object' | 'array'>
) => {
  return (dataString: string) => {
    const parseResult = parseJobData<T>(dataString, expectedShape);
    
    if (!parseResult.isValid) {
      return parseResult;
    }

    const extractResult = extractRequiredFields(parseResult.data!, requiredFields);
    
    return {
      data: parseResult.data,
      isValid: extractResult.isValid,
      error: extractResult.error
    };
  };
};

/**
 * Validate job has required structure.
 * Usage: if (isValidJob(job)) { ... }
 */
export const isValidJob = (job: any): boolean =>
  job && 
  typeof job.id === 'string' && 
  typeof job.data === 'string';

/**
 * Extract job ID safely.
 * Usage: const jobId = getJobId(job)
 */
export const getJobId = (job: any): string =>
  job?.id || 'unknown';

/**
 * Create job result object.
 * Usage: return createJobResult(true, 'Success')
 */
export const createJobResult = (success: boolean, message?: string) => ({
  success,
  message: message || (success ? 'Job completed successfully' : 'Job failed')
});

/**
 * Log job step with consistent format.
 * Usage: logJobStep(logger, jobId, 'info', 'Processing order', { orderId })
 */
export const logJobStep = (
  logger: { info: (id: string, msg: string) => void; error: (id: string, msg: string) => void; warn: (id: string, msg: string) => void },
  jobId: string,
  level: 'info' | 'error' | 'warn',
  message: string,
  context?: Record<string, any>
) => {
  const contextStr = context ? ` ${JSON.stringify(context)}` : '';
  logger[level](jobId, `${message}${contextStr}`);
};
