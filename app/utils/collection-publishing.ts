// 🦠 BACTERIAL COLLECTION PUBLISHING UTILITIES
// Small, focused functions for collection publication management

import { executeQuery, executeMutation } from './shopify';

/**
 * GraphQL mutations for collection publishing.
 */
export const PUBLISH_COLLECTION_MUTATION = `#graphql
  mutation PublishablePublish($id: ID!, $publicationId: ID!) {
    publishablePublish(id: $id, input: {publicationId: $publicationId}) {
      publishable {
        publishedOnPublication(publicationId: $publicationId)
      }
      userErrors {
        field
        message
      }
    }
  }`;

export const UNPUBLISH_COLLECTION_MUTATION = `#graphql
  mutation PublishableUnpublish($id: ID!, $publicationId: ID!) {
    publishableUnpublish(id: $id, input: {publicationId: $publicationId}) {
      publishable {
        publishedOnPublication(publicationId: $publicationId)
      }
      userErrors {
        field
        message
      }
    }
  }`;

export const GET_PRODUCT_COLLECTIONS_QUERY = `#graphql
  query productCollections($id: ID!, $first: Int!, $after: String) {
    product(id: $id) {
      collections(first: $first, after: $after) {
        edges {
          node {
            id
            title
            resourcePublications(first: 250) {
              edges {
                node {
                  publication {
                    id
                    name
                  }
                  isPublished
                }
              }
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  }`;

export const GET_COLLECTION_PRODUCTS_QUERY = `#graphql
  query collectionProducts($id: ID!, $first: Int!, $after: String) {
    collection(id: $id) {
      products(first: $first, after: $after) {
        edges {
          node {
            id
            status
            resourcePublications(first: 250) {
              edges {
                node {
                  publication {
                    id
                  }
                  isPublished
                }
              }
            }
            variants(first: 250) {
              edges {
                node {
                  inventoryQuantity
                }
              }
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  }`;

/**
 * Fetch all collections for a product with pagination.
 * Usage: const collections = await fetchAllProductCollections(admin, productId, initialData)
 */
export const fetchAllProductCollections = async (
  admin: any,
  productId: string,
  initialCollectionsData: any
): Promise<any[]> => {
  let allCollections: any[] = initialCollectionsData.edges.map((edge: any) => edge.node);
  let hasNextPage = initialCollectionsData.pageInfo?.hasNextPage || false;
  let cursor: string | null = initialCollectionsData.pageInfo?.endCursor || null;

  while (hasNextPage) {
    const response = await executeQuery(admin, GET_PRODUCT_COLLECTIONS_QUERY, {
      id: productId,
      first: 250,
      after: cursor
    });

    const collectionsData = response.product?.collections;
    if (collectionsData?.edges) {
      allCollections = allCollections.concat(collectionsData.edges.map((edge: any) => edge.node));
    }
    hasNextPage = collectionsData?.pageInfo?.hasNextPage || false;
    cursor = collectionsData?.pageInfo?.endCursor || null;
  }

  return allCollections;
};

/**
 * Check if other products in collection are in stock and published.
 * Usage: const hasOthers = await hasOtherPublishedInStockProducts(admin, collectionId, publicationIds, excludeProductId)
 */
export const hasOtherPublishedInStockProducts = async (
  admin: any,
  collectionId: string,
  productPublicationIds: string[],
  excludeProductId: string
): Promise<boolean> => {
  let cursor: string | null = null;

  while (true) {
    const response = await executeQuery(admin, GET_COLLECTION_PRODUCTS_QUERY, {
      id: collectionId,
      first: 250,
      after: cursor
    });

    const productsInCollection = response.collection?.products?.edges || [];

    for (const productEdge of productsInCollection) {
      const product = productEdge.node;

      // Exclude the current product from the check
      if (product.id === excludeProductId) {
        continue;
      }

      const isInStock = product.variants.edges.some(
        (edge: any) => edge.node.inventoryQuantity > 0
      );
      
      const isPublishedInRelevantChannel = product.status === "ACTIVE" && 
        product.resourcePublications.edges.some(
          (pubEdge: any) => productPublicationIds.includes(pubEdge.node.publication.id) && pubEdge.node.isPublished
        );

      if (isInStock && isPublishedInRelevantChannel) {
        return true;
      }
    }

    if (!response.collection?.products?.pageInfo?.hasNextPage) {
      break;
    }
    cursor = response.collection?.products?.pageInfo?.endCursor;
  }

  return false;
};

/**
 * Determine if collection should be published.
 * Usage: const shouldPublish = await shouldCollectionBePublished(admin, isProductInStock, collectionId, productPublicationIds, productId)
 */
export const shouldCollectionBePublished = async (
  admin: any,
  isProductInStock: boolean,
  collectionId: string,
  productPublicationIds: string[],
  productId: string
): Promise<boolean> => {
  if (isProductInStock) {
    return true; // If current product is in stock, collection should be published
  }
  
  // If current product is out of stock, check if other products in collection are in stock and published
  return hasOtherPublishedInStockProducts(admin, collectionId, productPublicationIds, productId);
};

/**
 * Publish collection to channel.
 * Usage: await publishCollection(admin, collectionId, publicationId)
 */
export const publishCollection = async (
  admin: any,
  collectionId: string,
  publicationId: string
): Promise<{ success: boolean; errors: any[] }> => {
  const response = await executeMutation(admin, PUBLISH_COLLECTION_MUTATION, {
    id: collectionId,
    publicationId
  });

  const errors = response.publishablePublish?.userErrors || [];
  return {
    success: errors.length === 0,
    errors
  };
};

/**
 * Unpublish collection from channel.
 * Usage: await unpublishCollection(admin, collectionId, publicationId)
 */
export const unpublishCollection = async (
  admin: any,
  collectionId: string,
  publicationId: string
): Promise<{ success: boolean; errors: any[] }> => {
  const response = await executeMutation(admin, UNPUBLISH_COLLECTION_MUTATION, {
    id: collectionId,
    publicationId
  });

  const errors = response.publishableUnpublish?.userErrors || [];
  return {
    success: errors.length === 0,
    errors
  };
};

/**
 * Get all available publications (sales channels).
 * Usage: const publications = await getAllPublications(admin)
 */
export const getAllPublications = async (admin: any): Promise<any[]> => {
  const GET_ALL_PUBLICATIONS_QUERY = `#graphql
    query getAllPublications($first: Int!, $after: String) {
      publications(first: $first, after: $after) {
        pageInfo { hasNextPage endCursor }
        edges {
          node {
            id
            name
          }
        }
      }
    }`;

  let allPublications: any[] = [];
  let cursor: string | null = null;
  let hasNextPage = true;

  while (hasNextPage) {
    const response = await executeQuery(admin, GET_ALL_PUBLICATIONS_QUERY, {
      first: 100,
      after: cursor
    });

    const pubsData = response.publications;
    if (pubsData?.edges) {
      allPublications = allPublications.concat(pubsData.edges.map((edge: any) => edge.node));
    }
    hasNextPage = pubsData?.pageInfo?.hasNextPage || false;
    cursor = pubsData?.pageInfo?.endCursor || null;
  }

  return allPublications;
};

/**
 * Check if collection is published in a specific channel.
 * Usage: const isPublished = isCollectionPublishedInChannel(collection, publicationId)
 */
export const isCollectionPublishedInChannel = (collection: any, publicationId: string): boolean => {
  return collection.resourcePublications.edges.some(
    (edge: any) => edge.node.publication.id === publicationId && edge.node.isPublished
  );
};

/**
 * Process collection publication for a single collection.
 * ✅ FIXED: Now iterates over ALL publications, not just current collection publications
 * Usage: await processCollectionPublication(admin, collection, shouldPublish, productPublicationIds, logger, jobId)
 */
export const processCollectionPublication = async (
  admin: any,
  collection: any,
  shouldPublish: boolean,
  productPublicationIds: string[],
  logger: {
    info: (jobId: string, message: string) => void;
    error: (jobId: string, message: string) => void;
  },
  jobId: string
): Promise<void> => {
  logger.info(jobId, `🔄 ENTERING processCollectionPublication for: ${collection.title} (ID: ${collection.id})`);
  logger.info(jobId, `🔄 Collection ${collection.title} - Should be published: ${shouldPublish}`);
  logger.info(jobId, `🔄 Product publication IDs: ${JSON.stringify(productPublicationIds)}`);
  logger.info(jobId, `🔄 Collection has ${collection.resourcePublications.edges.length} current publication edges`);

  // ✅ FIXED: Get ALL available publications, not just current collection publications
  const allPublications = await getAllPublications(admin);
  logger.info(jobId, `🔄 Found ${allPublications.length} total available publications`);

  // ✅ FIXED: Iterate over ALL publications, not just current collection publications
  for (const publication of allPublications) {
    const publicationId = publication.id;
    const publicationName = publication.name;

    // ✅ FIXED: Check if collection is published in this channel
    const isPublishedInThisChannel = isCollectionPublishedInChannel(collection, publicationId);

    logger.info(jobId, `🔄 Processing publication: ${publicationName} (ID: ${publicationId})`);
    logger.info(jobId, `🔄 Collection already published in ${publicationName}: ${isPublishedInThisChannel}`);
    logger.info(jobId, `🔄 DEBUG: collection.resourcePublications.edges.length = ${collection.resourcePublications.edges.length}`);

    if (shouldPublish) {
      logger.info(jobId, `🔄 PUBLISH logic for ${publicationName}`);
      logger.info(jobId, `🔄 DEBUG: isPublishedInThisChannel = ${isPublishedInThisChannel}, !isPublishedInThisChannel = ${!isPublishedInThisChannel}`);
      // Logic to PUBLISH collection
      if (!isPublishedInThisChannel) {
        logger.info(jobId, `🔄 Collection not published in ${publicationName}, checking product publication`);
        // Check if the product that triggered this is published in this channel
        const isProductPublishedInThisChannel = productPublicationIds.includes(publicationId);
        logger.info(jobId, `🔄 Product published in ${publicationName}: ${isProductPublishedInThisChannel}`);

        if (isProductPublishedInThisChannel) {
          logger.info(jobId, `✅ Publishing collection ${collection.title} to ${publicationName}`);
          const result = await publishCollection(admin, collection.id, publicationId);

          if (!result.success) {
            logger.error(jobId, `❌ Failed to publish collection ${collection.title}: ${JSON.stringify(result.errors)}`);
          } else {
            logger.info(jobId, `✅ Successfully published collection ${collection.title} to ${publicationName}`);
          }
        } else {
          logger.info(jobId, `⏭️ Skipping publishing collection ${collection.title} to ${publicationName} as the triggering product is not published there`);
        }
      } else {
        logger.info(jobId, `⏭️ Collection ${collection.title} already published to ${publicationName}`);
      }
    } else {
      logger.info(jobId, `🔄 UNPUBLISH logic for ${publicationName}`);
      // Logic to UNPUBLISH collection
      if (isPublishedInThisChannel) {
        logger.info(jobId, `✅ Unpublishing collection ${collection.title} from ${publicationName}`);
        const result = await unpublishCollection(admin, collection.id, publicationId);

        if (!result.success) {
          logger.error(jobId, `❌ Failed to unpublish collection ${collection.title}: ${JSON.stringify(result.errors)}`);
        } else {
          logger.info(jobId, `✅ Successfully unpublished collection ${collection.title} from ${publicationName}`);
        }
      } else {
        logger.info(jobId, `⏭️ Collection ${collection.title} already unpublished from ${publicationName}`);
      }
    }
  }
};
