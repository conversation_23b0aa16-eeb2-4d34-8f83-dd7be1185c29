// 🦠 BACTERIAL COLLECTION PUBLISHING UTILITIES
// Small, focused functions for collection publication management

import { executeQuery, executeMutation } from './shopify';

/**
 * GraphQL mutations for collection publishing.
 */
export const PUBLISH_COLLECTION_MUTATION = `#graphql
  mutation PublishablePublish($id: ID!, $publicationId: ID!) {
    publishablePublish(id: $id, input: {publicationId: $publicationId}) {
      publishable {
        publishedOnPublication(publicationId: $publicationId)
      }
      userErrors {
        field
        message
      }
    }
  }`;

export const UNPUBLISH_COLLECTION_MUTATION = `#graphql
  mutation PublishableUnpublish($id: ID!, $publicationId: ID!) {
    publishableUnpublish(id: $id, input: {publicationId: $publicationId}) {
      publishable {
        publishedOnPublication(publicationId: $publicationId)
      }
      userErrors {
        field
        message
      }
    }
  }`;

export const GET_PRODUCT_COLLECTIONS_QUERY = `#graphql
  query productCollections($id: ID!, $first: Int!, $after: String) {
    product(id: $id) {
      collections(first: $first, after: $after) {
        edges {
          node {
            id
            title
            resourcePublications(first: 250) {
              edges {
                node {
                  publication {
                    id
                    name
                  }
                  isPublished
                }
              }
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  }`;

export const GET_COLLECTION_PRODUCTS_QUERY = `#graphql
  query collectionProducts($id: ID!, $first: Int!, $after: String) {
    collection(id: $id) {
      products(first: $first, after: $after) {
        edges {
          node {
            id
            status
            resourcePublications(first: 250) {
              edges {
                node {
                  publication {
                    id
                  }
                  isPublished
                }
              }
            }
            variants(first: 250) {
              edges {
                node {
                  inventoryQuantity
                }
              }
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }
  }`;

/**
 * Fetch all collections for a product with pagination.
 * Usage: const collections = await fetchAllProductCollections(admin, productId, initialData)
 */
export const fetchAllProductCollections = async (
  admin: any,
  productId: string,
  initialCollectionsData: any
): Promise<any[]> => {
  let allCollections: any[] = initialCollectionsData.edges.map((edge: any) => edge.node);
  let hasNextPage = initialCollectionsData.pageInfo?.hasNextPage || false;
  let cursor: string | null = initialCollectionsData.pageInfo?.endCursor || null;

  while (hasNextPage) {
    const response = await executeQuery(admin, GET_PRODUCT_COLLECTIONS_QUERY, {
      id: productId,
      first: 250,
      after: cursor
    });

    const collectionsData = response.product?.collections;
    if (collectionsData?.edges) {
      allCollections = allCollections.concat(collectionsData.edges.map((edge: any) => edge.node));
    }
    hasNextPage = collectionsData?.pageInfo?.hasNextPage || false;
    cursor = collectionsData?.pageInfo?.endCursor || null;
  }

  return allCollections;
};

/**
 * Check if other products in collection are in stock and published.
 * Usage: const hasOthers = await hasOtherPublishedInStockProducts(admin, collectionId, publicationIds, excludeProductId)
 */
export const hasOtherPublishedInStockProducts = async (
  admin: any,
  collectionId: string,
  productPublicationIds: string[],
  excludeProductId: string
): Promise<boolean> => {
  let cursor: string | null = null;

  while (true) {
    const response = await executeQuery(admin, GET_COLLECTION_PRODUCTS_QUERY, {
      id: collectionId,
      first: 250,
      after: cursor
    });

    const productsInCollection = response.collection?.products?.edges || [];

    for (const productEdge of productsInCollection) {
      const product = productEdge.node;

      // Exclude the current product from the check
      if (product.id === excludeProductId) {
        continue;
      }

      const isInStock = product.variants.edges.some(
        (edge: any) => edge.node.inventoryQuantity > 0
      );
      
      const isPublishedInRelevantChannel = product.status === "ACTIVE" && 
        product.resourcePublications.edges.some(
          (pubEdge: any) => productPublicationIds.includes(pubEdge.node.publication.id) && pubEdge.node.isPublished
        );

      if (isInStock && isPublishedInRelevantChannel) {
        return true;
      }
    }

    if (!response.collection?.products?.pageInfo?.hasNextPage) {
      break;
    }
    cursor = response.collection?.products?.pageInfo?.endCursor;
  }

  return false;
};

/**
 * Determine if collection should be published.
 * Usage: const shouldPublish = await shouldCollectionBePublished(admin, isProductInStock, collectionId, productPublicationIds, productId)
 */
export const shouldCollectionBePublished = async (
  admin: any,
  isProductInStock: boolean,
  collectionId: string,
  productPublicationIds: string[],
  productId: string
): Promise<boolean> => {
  if (isProductInStock) {
    return true; // If current product is in stock, collection should be published
  }
  
  // If current product is out of stock, check if other products in collection are in stock and published
  return hasOtherPublishedInStockProducts(admin, collectionId, productPublicationIds, productId);
};

/**
 * Publish collection to channel.
 * Usage: await publishCollection(admin, collectionId, publicationId)
 */
export const publishCollection = async (
  admin: any,
  collectionId: string,
  publicationId: string
): Promise<{ success: boolean; errors: any[] }> => {
  const response = await executeMutation(admin, PUBLISH_COLLECTION_MUTATION, {
    id: collectionId,
    publicationId
  });

  const errors = response.publishablePublish?.userErrors || [];
  return {
    success: errors.length === 0,
    errors
  };
};

/**
 * Unpublish collection from channel.
 * Usage: await unpublishCollection(admin, collectionId, publicationId)
 */
export const unpublishCollection = async (
  admin: any,
  collectionId: string,
  publicationId: string
): Promise<{ success: boolean; errors: any[] }> => {
  const response = await executeMutation(admin, UNPUBLISH_COLLECTION_MUTATION, {
    id: collectionId,
    publicationId
  });

  const errors = response.publishableUnpublish?.userErrors || [];
  return {
    success: errors.length === 0,
    errors
  };
};

/**
 * Process collection publication for a single collection.
 * Usage: await processCollectionPublication(admin, collection, shouldPublish, productPublicationIds, logger, jobId)
 */
export const processCollectionPublication = async (
  admin: any,
  collection: any,
  shouldPublish: boolean,
  productPublicationIds: string[],
  logger: {
    info: (jobId: string, message: string) => void;
    error: (jobId: string, message: string) => void;
  },
  jobId: string
): Promise<void> => {
  logger.info(jobId, `Processing collection: ${collection.title} (ID: ${collection.id})`);
  logger.info(jobId, `Collection ${collection.title} - Should be published: ${shouldPublish}`);

  for (const pubEdge of collection.resourcePublications.edges) {
    const publicationNode = pubEdge.node;
    const isCollectionPublishedInChannel = publicationNode.isPublished;
    const publicationId = publicationNode.publication.id;
    const publicationName = publicationNode.publication.name;

    if (shouldPublish) {
      // Logic to PUBLISH collection
      if (!isCollectionPublishedInChannel) {
        // Check if the product that triggered this is published in this channel
        const isProductPublishedInThisChannel = productPublicationIds.includes(publicationId);
        
        if (isProductPublishedInThisChannel) {
          logger.info(jobId, `Publishing collection ${collection.title} to ${publicationName}`);
          const result = await publishCollection(admin, collection.id, publicationId);
          
          if (!result.success) {
            logger.error(jobId, `Failed to publish collection ${collection.title}: ${JSON.stringify(result.errors)}`);
          }
        } else {
          logger.info(jobId, `Skipping publishing collection ${collection.title} to ${publicationName} as the triggering product is not published there`);
        }
      }
    } else {
      // Logic to UNPUBLISH collection
      if (isCollectionPublishedInChannel) {
        logger.info(jobId, `Unpublishing collection ${collection.title} from ${publicationName}`);
        const result = await unpublishCollection(admin, collection.id, publicationId);
        
        if (!result.success) {
          logger.error(jobId, `Failed to unpublish collection ${collection.title}: ${JSON.stringify(result.errors)}`);
        }
      }
    }
  }
};
