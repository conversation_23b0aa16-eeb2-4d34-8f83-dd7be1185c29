// 🦠 BACTERIAL JOB CLEANUP UTILITIES
// Small, focused functions for job maintenance and cleanup

/**
 * Default timeout for stuck jobs in minutes.
 */
export const DEFAULT_STUCK_JOB_TIMEOUT_MINUTES = 30;

/**
 * App settings structure for job cleanup.
 */
export interface AppSettings {
  settings?: {
    stuckJobTimeout?: number;
  } | null;
}

/**
 * Extract stuck job timeout from app settings.
 * Usage: const timeout = extractStuckJobTimeout(appSettings)
 */
export const extractStuckJobTimeout = (appSettings: AppSettings | null): number => {
  if (!appSettings?.settings || typeof appSettings.settings !== 'object') {
    return DEFAULT_STUCK_JOB_TIMEOUT_MINUTES;
  }

  const settings = appSettings.settings as any;
  if ('stuckJobTimeout' in settings && typeof settings.stuckJobTimeout === 'number') {
    return settings.stuckJobTimeout;
  }

  return DEFAULT_STUCK_JOB_TIMEOUT_MINUTES;
};

/**
 * Calculate timeout date from minutes.
 * Usage: const timeoutDate = calculateTimeoutDate(30)
 */
export const calculateTimeoutDate = (timeoutMinutes: number): Date => {
  const timeoutMs = timeoutMinutes * 60 * 1000;
  return new Date(Date.now() - timeoutMs);
};

/**
 * Create error message for stuck jobs.
 * Usage: const message = createStuckJobErrorMessage(30)
 */
export const createStuckJobErrorMessage = (timeoutMinutes: number): string =>
  `Job timed out and was marked as failed by the system. It was in processing for more than ${timeoutMinutes} minutes.`;

/**
 * Find stuck jobs in database.
 * Usage: const jobs = await findStuckJobs(prisma, timeoutDate)
 */
export const findStuckJobs = async (
  prisma: any,
  timeoutDate: Date
): Promise<any[]> => {
  return prisma.job.findMany({
    where: {
      status: "processing",
      startedAt: {
        lt: timeoutDate,
      },
    },
  });
};

/**
 * Mark job as failed due to timeout.
 * Usage: await markJobAsFailed(prisma, jobId, errorMessage)
 */
export const markJobAsFailed = async (
  prisma: any,
  jobId: string,
  errorMessage: string
): Promise<void> => {
  await prisma.job.update({
    where: { id: jobId },
    data: {
      status: "failed",
      errorMessage,
    },
  });
};

/**
 * Process stuck job cleanup.
 * Usage: const result = await processStuckJobCleanup(prisma, appSettings, logger)
 */
export const processStuckJobCleanup = async (
  prisma: any,
  appSettings: AppSettings | null,
  logger: {
    info: (context: string, message: string) => void;
    warn: (context: string, message: string) => void;
    error: (context: string, message: string) => void;
  }
): Promise<{ success: boolean; processedCount: number; error?: string }> => {
  const logContext = "stuck-job-cleanup";
  
  try {
    // Extract timeout configuration
    const timeoutMinutes = extractStuckJobTimeout(appSettings);
    const timeoutDate = calculateTimeoutDate(timeoutMinutes);
    
    logger.info(logContext, `Looking for jobs stuck longer than ${timeoutMinutes} minutes`);

    // Find stuck jobs
    const stuckJobs = await findStuckJobs(prisma, timeoutDate);

    if (stuckJobs.length === 0) {
      logger.info(logContext, "No stuck jobs found");
      return { success: true, processedCount: 0 };
    }

    logger.info(logContext, `Found ${stuckJobs.length} stuck jobs to process`);

    // Process each stuck job
    const errorMessage = createStuckJobErrorMessage(timeoutMinutes);
    
    for (const job of stuckJobs) {
      await markJobAsFailed(prisma, job.id, errorMessage);
      logger.warn(logContext, `Marked job ${job.id} as failed due to timeout`);
    }

    logger.info(logContext, `Successfully processed ${stuckJobs.length} stuck jobs`);
    return { success: true, processedCount: stuckJobs.length };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(logContext, `Error during stuck job cleanup: ${errorMessage}`);
    return { success: false, processedCount: 0, error: errorMessage };
  }
};

/**
 * Get app settings from database.
 * Usage: const settings = await getAppSettings(prisma)
 */
export const getAppSettings = async (prisma: any): Promise<AppSettings | null> => {
  return prisma.appSetting.findFirst();
};

/**
 * Validate stuck job timeout value.
 * Usage: const isValid = isValidTimeout(30)
 */
export const isValidTimeout = (timeoutMinutes: number): boolean =>
  typeof timeoutMinutes === 'number' && 
  timeoutMinutes > 0 && 
  timeoutMinutes <= 1440; // Max 24 hours

/**
 * Create stuck job cleanup context for logging.
 * Usage: const context = createCleanupContext(timeoutMinutes, stuckJobCount)
 */
export const createCleanupContext = (
  timeoutMinutes: number,
  stuckJobCount: number
): Record<string, any> => ({
  timeoutMinutes,
  stuckJobCount,
  timeoutDate: calculateTimeoutDate(timeoutMinutes).toISOString()
});
