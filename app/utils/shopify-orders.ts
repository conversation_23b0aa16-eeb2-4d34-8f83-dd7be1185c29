// 🦠 BACTERIAL SHOPIFY ORDER UTILITIES
// Small, focused functions for order operations

import { executeQuery, executeMutation } from './shopify';
import { 
  GET_ORDER_FOR_CANCELLATION, 
  CANCEL_ORDER_MUTATION, 
  ADD_TAGS_MUTATION,
  GET_ORDER_BASIC,
  GET_ORDER_RISK 
} from './shopify-queries';

/**
 * Shopify order cancellation reasons.
 */
export const ORDER_CANCELLATION_REASONS = [
  'customer', 'declined', 'fraud', 'inventory', 'other', 'staff'
] as const;

export type OrderCancellationReason = typeof ORDER_CANCELLATION_REASONS[number];

/**
 * Check if order is already cancelled.
 * Usage: if (isOrderCancelled(order)) { ... }
 */
export const isOrderCancelled = (order: any): boolean =>
  Boolean(order?.cancelledAt);

/**
 * Check if order is paid.
 * Usage: if (isOrderPaid(order)) { ... }
 */
export const isOrderPaid = (order: any): boolean =>
  order?.displayFinancialStatus === 'PAID';

/**
 * Check if order is fulfilled or partially fulfilled.
 * Usage: if (isOrderFulfilled(order)) { ... }
 */
export const isOrderFulfilled = (order: any): boolean => {
  const status = order?.displayFulfillmentStatus;
  return status === 'FULFILLED' || status === 'PARTIALLY_FULFILLED';
};

/**
 * Check if order has high risk assessment.
 * Usage: if (hasHighRisk(order)) { ... }
 */
export const hasHighRisk = (order: any): boolean =>
  order?.risk?.assessments?.some((assessment: any) => 
    assessment.riskLevel === 'HIGH'
  ) || false;

/**
 * Extract user errors from Shopify GraphQL response.
 * Usage: const errors = extractUserErrors(response.data?.orderCancel)
 */
export const extractUserErrors = (mutationResult: any): any[] =>
  mutationResult?.userErrors || [];

/**
 * Check if mutation has errors.
 * Usage: if (hasMutationErrors(response.data?.orderCancel)) { ... }
 */
export const hasMutationErrors = (mutationResult: any): boolean =>
  extractUserErrors(mutationResult).length > 0;

/**
 * Get order for cancellation processing.
 * Bacterial function: focused on cancellation use case.
 */
export const getOrderForCancellation = async (
  admin: any, 
  orderId: string
): Promise<any> => {
  const response = await executeQuery(admin, GET_ORDER_FOR_CANCELLATION, { orderId });
  return response.order;
};

/**
 * Get basic order information.
 * Bacterial function: minimal order data.
 */
export const getOrderBasic = async (
  admin: any, 
  orderId: string
): Promise<any> => {
  const response = await executeQuery(admin, GET_ORDER_BASIC, { orderId });
  return response.order;
};

/**
 * Get order risk assessment.
 * Bacterial function: focused on risk data.
 */
export const getOrderRisk = async (
  admin: any, 
  orderId: string
): Promise<any> => {
  const response = await executeQuery(admin, GET_ORDER_RISK, { orderId });
  return response.order;
};

/**
 * Cancel order with specified options.
 * Bacterial function: focused order cancellation.
 */
export const cancelOrder = async (
  admin: any,
  orderId: string,
  options: {
    reason: OrderCancellationReason;
    notifyCustomer?: boolean;
    refund?: boolean;
    restock?: boolean;
    staffNote?: string;
  }
): Promise<{ success: boolean; errors: any[] }> => {
  const response = await executeMutation(admin, CANCEL_ORDER_MUTATION, {
    orderId,
    notifyCustomer: options.notifyCustomer || false,
    reason: options.reason.toUpperCase(),
    refund: options.refund || false,
    restock: options.restock || false,
    staffNote: options.staffNote || '',
  });

  const errors = extractUserErrors(response.orderCancel);
  return {
    success: errors.length === 0,
    errors
  };
};

/**
 * Add tags to order.
 * Bacterial function: focused tagging operation.
 */
export const addOrderTags = async (
  admin: any,
  orderId: string,
  tags: string[]
): Promise<{ success: boolean; errors: any[] }> => {
  const cleanTags = tags.filter(tag => tag.trim().length > 0);
  
  if (cleanTags.length === 0) {
    return { success: true, errors: [] };
  }

  const response = await executeMutation(admin, ADD_TAGS_MUTATION, {
    id: orderId,
    tags: cleanTags
  });

  const errors = extractUserErrors(response.tagsAdd);
  return {
    success: errors.length === 0,
    errors
  };
};

/**
 * Check if order can be cancelled.
 * Bacterial function: cancellation eligibility check.
 */
export const canCancelOrder = (order: any): { canCancel: boolean; reason?: string } => {
  if (isOrderCancelled(order)) {
    return { canCancel: false, reason: 'Order is already cancelled' };
  }

  if (isOrderFulfilled(order)) {
    return { canCancel: false, reason: 'Order is fulfilled or partially fulfilled' };
  }

  return { canCancel: true };
};
