// 🦠 BACTERIAL BULK OPERATION UTILITIES
// Small, focused functions for bulk operation processing

import { addOrderTags } from './shopify-orders';
import { logJobStep } from './job-data';

/**
 * Execute bulk tagging operation.
 * Usage: const result = await executeBulkTagging(admin, taggingMap, logger, logContext)
 */
export const executeBulkTagging = async (
  admin: any,
  tagsToAddToOrder: Map<string, Set<string>>,
  logger: {
    info: (context: string, message: string) => void;
    error: (context: string, message: string) => void;
  },
  logContext: string
): Promise<{ processedCount: number; errorCount: number }> => {
  let processedCount = 0;
  let errorCount = 0;
  
  for (const [orderId, tags] of tagsToAddToOrder.entries()) {
    if (tags.size > 0) {
      const tagsArray = Array.from(tags);
      logger.info(logContext, `Adding tags [${tagsArray.join(', ')}] to order ${orderId}`);
      
      try {
        const result = await addOrderTags(admin, orderId, tagsArray);
        if (result.success) {
          processedCount++;
        } else {
          errorCount++;
          logger.error(logContext, `Failed to apply tags to ${orderId}: ${JSON.stringify(result.errors)}`);
        }
      } catch (error) {
        errorCount++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error(logContext, `Failed to apply tags to ${orderId}: ${errorMessage}`);
      }
    }
  }
  
  return { processedCount, errorCount };
};

/**
 * Create bulk operation logger.
 * Usage: const logger = createBulkLogger(logToFile)
 */
export const createBulkLogger = (
  logFn: (context: string, level: any, message: string, ...args: any[]) => void
) => ({
  info: (context: string, message: string) => logFn(context, 'info', message),
  error: (context: string, message: string) => logFn(context, 'error', message),
  warn: (context: string, message: string) => logFn(context, 'warn', message),
  debug: (context: string, message: string) => logFn(context, 'debug', message),
});

/**
 * Create bulk operation context identifier.
 * Usage: const context = createBulkContext('order-collection-tag', shop)
 */
export const createBulkContext = (operation: string, shop: string): string =>
  `bulk-${operation}-${shop}`;

/**
 * Log bulk operation step with consistent formatting.
 * Usage: logBulkStep(logger, context, 'info', 'Step 1/3', 'Processing data', { count: 100 })
 */
export const logBulkStep = (
  logger: { info: (context: string, message: string) => void },
  context: string,
  step: string,
  description: string,
  metadata?: Record<string, any>
) => {
  const metaStr = metadata ? ` ${JSON.stringify(metadata)}` : '';
  logger.info(context, `[${step}] ${description}${metaStr}`);
};

/**
 * Validate bulk operation config.
 * Usage: const validation = validateBulkConfig(config, ['collections_and_tags'])
 */
export const validateBulkConfig = (
  config: any,
  requiredFields: string[]
): { isValid: boolean; error?: string } => {
  if (!config || typeof config !== 'object') {
    return { isValid: false, error: 'Config is required and must be an object' };
  }
  
  for (const field of requiredFields) {
    if (!config[field]) {
      return { isValid: false, error: `Missing required config field: ${field}` };
    }
  }
  
  return { isValid: true };
};

/**
 * Create bulk operation result.
 * Usage: return createBulkResult(processedCount, errorCount, 'Orders tagged successfully')
 */
export const createBulkResult = (
  processedCount: number,
  errorCount: number = 0,
  message?: string
) => ({
  processedCount,
  errorCount,
  success: errorCount === 0,
  message: message || `Processed ${processedCount} items${errorCount > 0 ? ` with ${errorCount} errors` : ''}`
});

/**
 * Process bulk operation with error handling and logging.
 * Usage: return await processBulkOperation(context, lines, processor, logger)
 */
export const processBulkOperation = async <T, R>(
  context: string,
  data: T,
  processor: (data: T) => Promise<R>,
  logger: { info: (context: string, message: string) => void; error: (context: string, message: string) => void }
): Promise<R | { processedCount: 0; error: string }> => {
  try {
    logger.info(context, 'Starting bulk operation processing');
    const result = await processor(data);
    logger.info(context, 'Bulk operation completed successfully');
    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(context, `Bulk operation failed: ${errorMessage}`);
    return { processedCount: 0, error: errorMessage };
  }
};
