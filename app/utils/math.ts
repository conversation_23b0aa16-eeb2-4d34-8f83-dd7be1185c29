// 🦠 BACTERIAL MATH UTILITIES
// Small, pure, self-contained mathematical functions

/**
 * Clamp a number between min and max values.
 * Usage: clamp(15, 1, 10) → 10
 */
export const clamp = (value: number, min: number, max: number): number => 
  Math.min(Math.max(value, min), max);

/**
 * Check if number is between two values (inclusive).
 * Usage: isBetween(5, 1, 10) → true
 */
export const isBetween = (value: number, min: number, max: number): boolean =>
  value >= min && value <= max;

/**
 * Calculate percentage of value relative to total.
 * Usage: percentage(25, 100) → 25
 */
export const percentage = (value: number, total: number): number =>
  total === 0 ? 0 : (value / total) * 100;

/**
 * Round number to specified decimal places.
 * Usage: roundTo(3.14159, 2) → 3.14
 */
export const roundTo = (value: number, decimals: number): number =>
  Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);

/**
 * Check if number is even.
 * Usage: isEven(4) → true
 */
export const isEven = (n: number): boolean => n % 2 === 0;

/**
 * Check if number is odd.
 * Usage: isOdd(3) → true
 */
export const isOdd = (n: number): boolean => n % 2 !== 0;

/**
 * Get random integer between min and max (inclusive).
 * Usage: randomInt(1, 10) → random number between 1 and 10
 */
export const randomInt = (min: number, max: number): number =>
  Math.floor(Math.random() * (max - min + 1)) + min;

/**
 * Calculate sum of array of numbers.
 * Usage: sum([1, 2, 3, 4]) → 10
 */
export const sum = (numbers: number[]): number =>
  numbers.reduce((acc, n) => acc + n, 0);

/**
 * Calculate average of array of numbers.
 * Usage: average([1, 2, 3, 4]) → 2.5
 */
export const average = (numbers: number[]): number =>
  numbers.length === 0 ? 0 : sum(numbers) / numbers.length;
