// 🦠 BACTERIAL ASYNC UTILITIES
// Small, pure, self-contained async helper functions

/**
 * Create a delay/sleep function.
 * Usage: await delay(1000) // Wait 1 second
 */
export const delay = (ms: number): Promise<void> =>
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Calculate exponential backoff delay.
 * Usage: calculateBackoff(3, 1000) → 4000ms (1000 * 2^(3-1))
 */
export const calculateBackoff = (attempt: number, baseDelayMs: number): number =>
  baseDelayMs * Math.pow(2, attempt - 1);

/**
 * Retry a function with exponential backoff.
 * Usage: await retryWithBackoff(() => apiCall(), 3, 1000)
 */
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelayMs: number = 1000,
  onRetry?: (attempt: number, error: Error) => void
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (attempt < maxRetries) {
        const delayMs = calculateBackoff(attempt, baseDelayMs);
        onRetry?.(attempt, lastError);
        await delay(delayMs);
      }
    }
  }

  throw lastError!;
};

/**
 * Check if error indicates rate limiting.
 * Usage: if (isRateLimitError(error)) { ... }
 */
export const isRateLimitError = (error: any): boolean => {
  if (!error) return false;
  
  const message = error.message || '';
  const code = error.extensions?.code || error.code || '';
  
  return (
    message.includes('Throttled') ||
    message.includes('rate limit') ||
    message.includes('Rate limit') ||
    code === 'THROTTLED' ||
    code === 'RATE_LIMITED'
  );
};

/**
 * Extract GraphQL errors from response.
 * Usage: const errors = extractGraphQLErrors(response)
 */
export const extractGraphQLErrors = (response: any): any[] =>
  response?.errors || [];

/**
 * Check if GraphQL response has errors.
 * Usage: if (hasGraphQLErrors(response)) { ... }
 */
export const hasGraphQLErrors = (response: any): boolean =>
  extractGraphQLErrors(response).length > 0;

/**
 * Find specific error type in GraphQL errors.
 * Usage: const rateLimitError = findErrorByType(errors, isRateLimitError)
 */
export const findErrorByType = (
  errors: any[], 
  predicate: (error: any) => boolean
): any | undefined =>
  errors.find(predicate);

/**
 * Create a timeout promise that rejects after specified time.
 * Usage: await Promise.race([apiCall(), timeout(5000)])
 */
export const timeout = (ms: number, message = 'Operation timed out'): Promise<never> =>
  new Promise((_, reject) => 
    setTimeout(() => reject(new Error(message)), ms)
  );

/**
 * Wrap a promise with timeout.
 * Usage: await withTimeout(apiCall(), 5000)
 */
export const withTimeout = <T>(
  promise: Promise<T>, 
  ms: number, 
  message?: string
): Promise<T> =>
  Promise.race([promise, timeout(ms, message)]);
