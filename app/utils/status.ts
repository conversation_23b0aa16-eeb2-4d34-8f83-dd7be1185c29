// 🦠 BACTERIAL STATUS UTILITIES
// Small, pure, self-contained status mapping functions

/**
 * Generic status to semantic mapping.
 * Bacterial utility: framework-agnostic status semantics.
 */
export const STATUS_SEMANTICS = {
  completed: 'success',
  success: 'success',
  done: 'success',
  finished: 'success',
  
  failed: 'error',
  error: 'error',
  rejected: 'error',
  
  processing: 'info',
  running: 'info',
  active: 'info',
  
  pending: 'warning',
  waiting: 'warning',
  queued: 'warning',
  
  retrying: 'warning',
  retry: 'warning',
  
  canceled: 'neutral',
  cancelled: 'neutral',
  stopped: 'neutral',
  paused: 'neutral',
} as const;

/**
 * Semantic status types for better type safety.
 */
export type SemanticStatus = 'success' | 'error' | 'info' | 'warning' | 'neutral';

/**
 * Map any status string to semantic meaning.
 * Usage: getStatusSemantic('completed') → 'success'
 */
export const getStatusSemantic = (status: string): SemanticStatus => {
  const normalized = status.toLowerCase();
  return STATUS_SEMANTICS[normalized as keyof typeof STATUS_SEMANTICS] || 'neutral';
};

/**
 * Check if status indicates completion.
 * Usage: isCompleted('done') → true
 */
export const isCompleted = (status: string): boolean =>
  getStatusSemantic(status) === 'success';

/**
 * Check if status indicates failure.
 * Usage: isFailed('error') → true
 */
export const isFailed = (status: string): boolean =>
  getStatusSemantic(status) === 'error';

/**
 * Check if status indicates active processing.
 * Usage: isProcessing('running') → true
 */
export const isProcessing = (status: string): boolean =>
  getStatusSemantic(status) === 'info';

/**
 * Check if status indicates waiting state.
 * Usage: isPending('queued') → true
 */
export const isPending = (status: string): boolean =>
  getStatusSemantic(status) === 'warning';

/**
 * Get status priority for sorting (lower number = higher priority).
 * Usage: getStatusPriority('failed') → 1 (highest priority)
 */
export const getStatusPriority = (status: string): number => {
  const semantic = getStatusSemantic(status);
  switch (semantic) {
    case 'error': return 1;    // Failed states need attention
    case 'warning': return 2;  // Pending/retrying states
    case 'info': return 3;     // Processing states
    case 'success': return 4;  // Completed states
    case 'neutral': return 5;  // Cancelled/neutral states
    default: return 6;
  }
};

/**
 * Sort statuses by priority (most important first).
 * Usage: ['completed', 'failed', 'pending'].sort(compareStatusPriority)
 */
export const compareStatusPriority = (a: string, b: string): number =>
  getStatusPriority(a) - getStatusPriority(b);
