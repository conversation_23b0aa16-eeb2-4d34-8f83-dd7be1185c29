// 🦠 BACTERIAL SHOPIFY GRAPHQL QUERIES
// Small, focused, reusable GraphQL query definitions

/**
 * Get order details for cancellation processing.
 * Bacterial approach: focused query for specific use case.
 */
export const GET_ORDER_FOR_CANCELLATION = `#graphql
  query getOrderForCancellation($orderId: ID!) {
    order(id: $orderId) {
      id
      name
      cancelledAt
      displayFinancialStatus
      displayFulfillmentStatus
      risk {
        assessments {
          riskLevel
        }
      }
    }
  }`;

/**
 * Cancel an order with all options.
 * Bacterial approach: focused mutation for order cancellation.
 */
export const CANCEL_ORDER_MUTATION = `#graphql
  mutation OrderCancel($orderId: ID!, $notifyCustomer: Boolean, $reason: OrderCancelReason!, $refund: Boolean!, $restock: Boolean!, $staffNote: String) {
    orderCancel(orderId: $orderId, notifyCustomer: $notifyCustomer, refund: $refund, restock: $restock, reason: $reason, staffNote: $staffNote) {
      job {
        id
      }
      userErrors {
        field
        message
      }
    }
  }`;

/**
 * Add tags to any Shopify resource.
 * Bacterial approach: generic tagging mutation.
 */
export const ADD_TAGS_MUTATION = `#graphql
  mutation tagsAdd($id: ID!, $tags: [String!]!) {
    tagsAdd(id: $id, tags: $tags) {
      userErrors {
        field
        message
      }
    }
  }`;

/**
 * Get basic order information.
 * Bacterial approach: minimal order query.
 */
export const GET_ORDER_BASIC = `#graphql
  query getOrderBasic($orderId: ID!) {
    order(id: $orderId) {
      id
      name
      cancelledAt
      displayFinancialStatus
      displayFulfillmentStatus
    }
  }`;

/**
 * Get order risk assessment.
 * Bacterial approach: focused on risk data only.
 */
export const GET_ORDER_RISK = `#graphql
  query getOrderRisk($orderId: ID!) {
    order(id: $orderId) {
      id
      risk {
        assessments {
          riskLevel
          message
          recommendation
        }
      }
    }
  }`;

/**
 * Update order tags (replace existing).
 * Bacterial approach: focused tag replacement.
 */
export const UPDATE_ORDER_TAGS = `#graphql
  mutation orderUpdate($input: OrderInput!) {
    orderUpdate(input: $input) {
      order {
        id
        tags
      }
      userErrors {
        field
        message
      }
    }
  }`;

/**
 * Get order financial transactions.
 * Bacterial approach: focused on payment data.
 */
export const GET_ORDER_TRANSACTIONS = `#graphql
  query getOrderTransactions($orderId: ID!) {
    order(id: $orderId) {
      id
      transactions {
        id
        status
        kind
        amount
        gateway
      }
    }
  }`;

/**
 * Create order note/timeline entry.
 * Bacterial approach: focused note creation.
 */
export const CREATE_ORDER_NOTE = `#graphql
  mutation orderUpdate($input: OrderInput!) {
    orderUpdate(input: $input) {
      order {
        id
      }
      userErrors {
        field
        message
      }
    }
  }`;
