// 🦠 BACTERIAL STRING UTILITIES
// Small, pure, self-contained string manipulation functions

/**
 * Convert snake_case or SCREAMING_SNAKE_CASE to Title Case.
 * Usage: toTitleCase("HELLO_WORLD") → "Hello World"
 */
export const toTitleCase = (str: string): string =>
  str.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

/**
 * Convert string to camelCase.
 * Usage: toCamelCase("hello_world") → "helloWorld"
 */
export const toCamelCase = (str: string): string =>
  str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());

/**
 * Convert string to kebab-case.
 * Usage: toKebabCase("Hello World") → "hello-world"
 */
export const toKebabCase = (str: string): string =>
  str.toLowerCase().replace(/[_\s]+/g, '-');

/**
 * Convert string to snake_case.
 * Usage: toSnakeCase("Hello World") → "hello_world"
 */
export const toSnakeCase = (str: string): string =>
  str.toLowerCase().replace(/[\s-]+/g, '_');

/**
 * Capitalize first letter of string.
 * Usage: capitalize("hello") → "Hello"
 */
export const capitalize = (str: string): string =>
  str.charAt(0).toUpperCase() + str.slice(1);

/**
 * Truncate string to specified length with ellipsis.
 * Usage: truncate("Hello World", 5) → "Hello..."
 */
export const truncate = (str: string, length: number, suffix = '...'): string =>
  str.length <= length ? str : str.slice(0, length) + suffix;

/**
 * Remove all whitespace from string.
 * Usage: removeWhitespace("h e l l o") → "hello"
 */
export const removeWhitespace = (str: string): string =>
  str.replace(/\s/g, '');

/**
 * Check if string is empty or only whitespace.
 * Usage: isBlank("   ") → true
 */
export const isBlank = (str: string): boolean =>
  str.trim().length === 0;

/**
 * Generate a random string of specified length.
 * Usage: randomString(8) → "aB3xY9mK"
 */
export const randomString = (length: number): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  return Array.from({ length }, () => chars.charAt(Math.floor(Math.random() * chars.length))).join('');
};

/**
 * Count occurrences of substring in string.
 * Usage: countOccurrences("hello world", "l") → 3
 */
export const countOccurrences = (str: string, substring: string): number =>
  (str.match(new RegExp(substring, 'g')) || []).length;
