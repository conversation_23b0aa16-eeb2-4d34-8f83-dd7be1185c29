// 🦠 BACTERIAL BULK VENDOR TAGGING UTILITIES
// Small, focused functions for bulk vendor-based customer tagging

import { filterByType, createLookupMap } from './bulk-data';

/**
 * Bulk vendor tagging configuration.
 */
export interface BulkVendorTaggingConfig {
  prefix: string;
}

/**
 * Extract vendor from line item.
 * Usage: const vendor = extractVendorFromLineItem(lineItem)
 */
export const extractVendorFromLineItem = (lineItem: any): string | null =>
  lineItem.vendor || null;

/**
 * Create vendor tag with prefix.
 * Usage: const tag = createVendorTag('Electronics', 'vendor-')
 */
export const createVendorTag = (vendor: string, prefix: string): string =>
  `${prefix}${vendor}`;

/**
 * Process line item for vendor tagging.
 * Usage: const result = processLineItemForVendor(lineItem, config)
 */
export const processLineItemForVendor = (
  lineItem: any,
  config: BulkVendorTaggingConfig
): { orderId: string; vendorTag: string } | null => {
  if (!lineItem.__parentId || !lineItem.vendor) {
    return null;
  }

  return {
    orderId: lineItem.__parentId,
    vendorTag: createVendorTag(lineItem.vendor, config.prefix)
  };
};

/**
 * Extract customer-vendor relationships from bulk data.
 * Usage: const relationships = extractCustomerVendorRelationships(lines, config)
 */
export const extractCustomerVendorRelationships = (
  lines: any[],
  config: BulkVendorTaggingConfig
): Map<string, Set<string>> => {
  const customerTags = new Map<string, Set<string>>();
  
  // Create lookup maps for efficient processing
  const orders = filterByType(lines, 'Order');
  const lineItems = filterByType(lines, 'LineItem');
  
  // Create order ID to customer ID mapping
  const orderToCustomer = createLookupMap(
    orders.filter(order => order.id && order.customer?.id),
    order => order.id,
    order => order.customer.id
  );

  // Process line items for vendor tags
  lineItems.forEach(lineItem => {
    const vendorResult = processLineItemForVendor(lineItem, config);
    if (!vendorResult) return;

    const { orderId, vendorTag } = vendorResult;
    const customerId = orderToCustomer.get(orderId);
    
    if (customerId) {
      if (!customerTags.has(customerId)) {
        customerTags.set(customerId, new Set());
      }
      customerTags.get(customerId)!.add(vendorTag);
    }
  });

  return customerTags;
};

/**
 * Validate bulk vendor tagging config.
 * Usage: const validation = validateBulkVendorConfig(config)
 */
export const validateBulkVendorConfig = (config: any): { isValid: boolean; error?: string } => {
  if (!config || typeof config !== 'object') {
    return { isValid: false, error: 'Config is required and must be an object' };
  }
  
  if (!config.prefix || typeof config.prefix !== 'string') {
    return { isValid: false, error: 'Config must have a prefix string' };
  }
  
  return { isValid: true };
};

/**
 * Create bulk vendor tagging context for logging.
 * Usage: const context = createBulkVendorContext(shop, lineCount, customerCount)
 */
export const createBulkVendorContext = (
  shop: string,
  lineCount: number,
  customerCount: number
): Record<string, any> => ({
  shop,
  lineCount,
  customerCount,
  operation: 'bulk-customer-vendor-tagging'
});

/**
 * Process bulk vendor tagging data.
 * Usage: const customerTags = processBulkVendorData(lines, config)
 */
export const processBulkVendorData = (
  lines: any[],
  config: BulkVendorTaggingConfig
): Map<string, Set<string>> => {
  return extractCustomerVendorRelationships(lines, config);
};

/**
 * Count total vendor tags to be applied.
 * Usage: const total = countVendorTags(customerTagsMap)
 */
export const countVendorTags = (customerTags: Map<string, Set<string>>): number => {
  let total = 0;
  customerTags.forEach(tags => {
    total += tags.size;
  });
  return total;
};

/**
 * Get unique vendors from line items.
 * Usage: const vendors = getUniqueVendors(lineItems)
 */
export const getUniqueVendors = (lineItems: any[]): Set<string> => {
  const vendors = new Set<string>();
  lineItems.forEach(item => {
    const vendor = extractVendorFromLineItem(item);
    if (vendor) {
      vendors.add(vendor);
    }
  });
  return vendors;
};
