// 🦠 BACTERIAL SHOPIFY COLLECTION UTILITIES
// Small, focused functions for collection operations

import { executeQuery } from './shopify';
import { 
  GET_ORDER_BASIC,
  GET_ORDER_FOR_CANCELLATION 
} from './shopify-queries';

/**
 * GraphQL queries for collection operations.
 */
export const GET_ORDER_TAGS_ONLY = `#graphql
  query getOrderTags($orderId: ID!) {
    order(id: $orderId) {
      tags
    }
  }`;

export const GET_ORDER_DETAILS_WITH_PRODUCTS = `#graphql
  query getOrderDetails($orderId: ID!) {
    order(id: $orderId) {
      tags
      lineItems(first: 250) {
        nodes {
          product {
            id
          }
        }
      }
    }
  }`;

export const GET_COLLECTIONS_FOR_PRODUCTS = `#graphql
  query getCollectionsForProducts($query: String!, $cursor: String) {
    collections(first: 250, query: $query, after: $cursor) {
      pageInfo { hasNextPage endCursor }
      nodes {
        id
        legacyResourceId
        title
        handle
      }
    }
  }`;

/**
 * Extract numeric ID from Shopify GID.
 * Usage: extractNumericId('gid://shopify/Product/123') → '123'
 */
export const extractNumericId = (gid: string): string => {
  const parts = gid.split('/');
  return parts[parts.length - 1];
};

/**
 * Build search query for collections by product IDs.
 * Usage: buildProductSearchQuery(['gid://shopify/Product/123', 'gid://shopify/Product/456'])
 */
export const buildProductSearchQuery = (productIds: string[]): string => {
  return productIds
    .map(id => `product_id:${extractNumericId(id)}`)
    .join(' OR ');
};

/**
 * Extract product IDs from order line items.
 * Usage: const productIds = extractProductIds(order.lineItems.nodes)
 */
export const extractProductIds = (lineItems: any[]): string[] => {
  return [
    ...new Set(
      lineItems
        .map(item => item.product?.id)
        .filter(Boolean)
    )
  ];
};

/**
 * Get order tags only.
 * Usage: const tags = await getOrderTags(admin, orderId)
 */
export const getOrderTags = async (admin: any, orderId: string): Promise<string[]> => {
  const response = await executeQuery(admin, GET_ORDER_TAGS_ONLY, { orderId });
  return response.order?.tags || [];
};

/**
 * Get order details including products.
 * Usage: const details = await getOrderDetailsWithProducts(admin, orderId)
 */
export const getOrderDetailsWithProducts = async (
  admin: any, 
  orderId: string
): Promise<{ tags: string[]; productIds: string[] } | null> => {
  const response = await executeQuery(admin, GET_ORDER_DETAILS_WITH_PRODUCTS, { orderId });
  const order = response.order;
  
  if (!order) {
    return null;
  }
  
  return {
    tags: order.tags || [],
    productIds: extractProductIds(order.lineItems?.nodes || [])
  };
};

/**
 * Fetch collections with pagination.
 * Usage: const collections = await fetchCollectionsPaginated(admin, searchQuery)
 */
export const fetchCollectionsPaginated = async (
  admin: any,
  searchQuery: string,
  onProgress?: (count: number) => void
): Promise<any[]> => {
  let allCollections: any[] = [];
  let cursor: string | null = null;
  let hasNextPage = true;

  while (hasNextPage) {
    const response = await executeQuery(admin, GET_COLLECTIONS_FOR_PRODUCTS, {
      query: searchQuery,
      cursor
    });
    
    const collectionsData = response.collections;
    if (collectionsData?.nodes) {
      allCollections = allCollections.concat(collectionsData.nodes);
      onProgress?.(allCollections.length);
    }
    
    hasNextPage = collectionsData?.pageInfo?.hasNextPage || false;
    cursor = collectionsData?.pageInfo?.endCursor || null;
  }

  return allCollections;
};

/**
 * Get collections for specific products.
 * Usage: const collections = await getCollectionsForProducts(admin, productIds, onProgress)
 */
export const getCollectionsForProducts = async (
  admin: any,
  productIds: string[],
  onProgress?: (count: number) => void
): Promise<any[]> => {
  if (productIds.length === 0) {
    return [];
  }
  
  const searchQuery = buildProductSearchQuery(productIds);
  return fetchCollectionsPaginated(admin, searchQuery, onProgress);
};

/**
 * Create collection identifiers for matching.
 * Usage: const identifiers = createCollectionIdentifierSet(collections)
 */
export const createCollectionIdentifierSet = (collections: any[]): Set<string> => {
  const identifiers = new Set<string>();
  
  collections.forEach(collection => {
    if (collection.id) identifiers.add(collection.id);
    if (collection.legacyResourceId) identifiers.add(collection.legacyResourceId);
    if (collection.title) identifiers.add(collection.title.toLowerCase());
    if (collection.handle) identifiers.add(collection.handle.toLowerCase());
  });
  
  return identifiers;
};

/**
 * Determine tags to add based on collection matches.
 * Usage: const tags = determineTagsToAdd(collectionIdentifiers, config, existingTags)
 */
export const determineTagsToAdd = (
  collectionIdentifiers: Set<string>,
  collectionTagPairs: Array<{ key: string; value: string }>,
  existingTags: Set<string>
): string[] => {
  const tagsToAdd = new Set<string>();
  
  for (const pair of collectionTagPairs) {
    if (collectionIdentifiers.has(pair.key.toLowerCase())) {
      const tagsFromConfig = pair.value
        .split(',')
        .map(tag => tag.trim())
        .filter(Boolean);
      
      for (const tag of tagsFromConfig) {
        if (!existingTags.has(tag)) {
          tagsToAdd.add(tag);
        }
      }
    }
  }
  
  return Array.from(tagsToAdd);
};
