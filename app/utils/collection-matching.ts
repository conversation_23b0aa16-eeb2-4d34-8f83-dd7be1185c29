// 🦠 BACTERIAL COLLECTION MATCHING UTILITIES
// Small, focused functions for collection-based tagging logic

/**
 * Create collection identifiers for matching.
 * Usage: const identifiers = createCollectionIdentifiers(collection)
 */
export const createCollectionIdentifiers = (collection: {
  id?: string;
  legacyResourceId?: string;
  title?: string;
  handle?: string;
}): Set<string> => {
  const identifiers = new Set<string>();
  
  if (collection.id) identifiers.add(collection.id);
  if (collection.legacyResourceId) identifiers.add(collection.legacyResourceId);
  if (collection.title) identifiers.add(collection.title.toLowerCase());
  if (collection.handle) identifiers.add(collection.handle.toLowerCase());
  
  return identifiers;
};

/**
 * Parse tag string into array of clean tags.
 * Usage: const tags = parseTagString("tag1, tag2, tag3")
 */
export const parseTagString = (tagString: string): string[] =>
  tagString
    .split(',')
    .map(tag => tag.trim())
    .filter(Boolean);

/**
 * Check if collection matches identifier.
 * Usage: if (matchesCollection(identifiers, 'summer-collection')) { ... }
 */
export const matchesCollection = (
  collectionIdentifiers: Set<string>, 
  targetIdentifier: string
): boolean => {
  const normalized = targetIdentifier.toLowerCase();
  return collectionIdentifiers.has(normalized);
};

/**
 * Find matching collections from a list.
 * Usage: const matches = findMatchingCollections(orderCollections, targetCollections)
 */
export const findMatchingCollections = (
  orderCollectionIdentifiers: Set<string>,
  targetCollections: Array<{ key: string; value: string }>
): Array<{ key: string; value: string }> => {
  return targetCollections.filter(collection => 
    matchesCollection(orderCollectionIdentifiers, collection.key)
  );
};

/**
 * Get tags to add for matched collections.
 * Usage: const tags = getTagsForCollections(matches, existingTags)
 */
export const getTagsForCollections = (
  matchedCollections: Array<{ key: string; value: string }>,
  existingTags: Set<string> = new Set()
): string[] => {
  const newTags = new Set<string>();
  
  matchedCollections.forEach(collection => {
    const tags = parseTagString(collection.value);
    tags.forEach(tag => {
      if (!existingTags.has(tag)) {
        newTags.add(tag);
      }
    });
  });
  
  return Array.from(newTags);
};

/**
 * Create collection-to-product mapping from bulk data.
 * Usage: const mapping = createCollectionProductMapping(lines, productLookup)
 */
export const createCollectionProductMapping = (
  collectionLines: Array<{
    __typename?: string;
    __parentId?: string;
    id?: string;
    legacyResourceId?: string;
    title?: string;
    handle?: string;
  }>,
  lineItemToProductMap: Map<string, string>
): Map<string, Set<string>> => {
  const productToCollections = new Map<string, Set<string>>();
  
  collectionLines
    .filter(line => line.__typename === 'Collection' && line.__parentId)
    .forEach(line => {
      const lineItemId = line.__parentId!;
      const productId = lineItemToProductMap.get(lineItemId);
      
      if (productId) {
        if (!productToCollections.has(productId)) {
          productToCollections.set(productId, new Set());
        }
        
        const identifiers = createCollectionIdentifiers(line);
        identifiers.forEach(id => {
          productToCollections.get(productId)!.add(id);
        });
      }
    });
  
  return productToCollections;
};

/**
 * Aggregate collection identifiers for an order.
 * Usage: const identifiers = aggregateOrderCollections(productIds, productCollectionMap)
 */
export const aggregateOrderCollections = (
  productIds: Set<string>,
  productToCollectionMap: Map<string, Set<string>>
): Set<string> => {
  const orderCollections = new Set<string>();
  
  productIds.forEach(productId => {
    const collections = productToCollectionMap.get(productId);
    if (collections) {
      collections.forEach(collection => {
        orderCollections.add(collection);
      });
    }
  });
  
  return orderCollections;
};

/**
 * Process collection-based tagging for multiple orders.
 * Usage: const taggingMap = processCollectionTagging(orderProductMap, productCollectionMap, config, existingTagsMap)
 */
export const processCollectionTagging = (
  orderToProductIds: Map<string, Set<string>>,
  productToCollectionIdentifiers: Map<string, Set<string>>,
  collectionTagPairs: Array<{ key: string; value: string }>,
  orderToExistingTags: Map<string, Set<string>>
): Map<string, Set<string>> => {
  const tagsToAddToOrder = new Map<string, Set<string>>();
  
  if (collectionTagPairs.length === 0) {
    return tagsToAddToOrder;
  }
  
  orderToProductIds.forEach((productIds, orderId) => {
    const orderCollectionIdentifiers = aggregateOrderCollections(
      productIds, 
      productToCollectionIdentifiers
    );
    
    if (orderCollectionIdentifiers.size === 0) {
      return;
    }
    
    const matchedCollections = findMatchingCollections(
      orderCollectionIdentifiers, 
      collectionTagPairs
    );
    
    if (matchedCollections.length > 0) {
      const existingTags = orderToExistingTags.get(orderId) || new Set();
      const newTags = getTagsForCollections(matchedCollections, existingTags);
      
      if (newTags.length > 0) {
        tagsToAddToOrder.set(orderId, new Set(newTags));
      }
    }
  });
  
  return tagsToAddToOrder;
};
