// 🦠 BACTERIAL CUSTOMER TAGGING UTILITIES
// Small, focused functions for customer tagging operations

import { executeQuery } from './shopify';

/**
 * Customer tagging configuration.
 */
export interface CustomerTagConfig {
  order_tags_and_customer_tags: Array<{ key: string; value: string }>;
}

/**
 * Order and customer data structure.
 */
export interface OrderCustomerData {
  order: {
    id: string;
    tags: string[];
    customer?: {
      id: string;
      tags: string[];
    };
  };
}

/**
 * GraphQL query for order and customer tags.
 */
export const GET_ORDER_AND_CUSTOMER_TAGS = `#graphql
  query getOrderAndCustomerTags($orderId: ID!) {
    order(id: $orderId) {
      id
      tags
      customer {
        id
        tags
      }
    }
  }`;

/**
 * Get order and customer data.
 * Usage: const data = await getOrderAndCustomerData(admin, orderId)
 */
export const getOrderAndCustomerData = async (
  admin: any,
  orderId: string
): Promise<OrderCustomerData | null> => {
  const response = await executeQuery(admin, GET_ORDER_AND_CUSTOMER_TAGS, { orderId });
  
  if (!response.order) {
    return null;
  }
  
  return { order: response.order };
};

/**
 * Check if order has an associated customer.
 * Usage: if (hasCustomer(orderData)) { ... }
 */
export const hasCustomer = (orderData: OrderCustomerData): boolean =>
  Boolean(orderData.order.customer?.id);

/**
 * Determine customer tags to apply based on order tags.
 * Usage: const tags = determineCustomerTags(orderTags, customerTags, mapping)
 */
export const determineCustomerTags = (
  orderTags: string[],
  customerTags: string[],
  tagMapping: Array<{ key: string; value: string }>
): string[] => {
  const orderTagsSet = new Set(orderTags);
  const customerTagsSet = new Set(customerTags);
  
  return tagMapping
    .filter(pair => orderTagsSet.has(pair.key) && !customerTagsSet.has(pair.value))
    .map(pair => pair.value);
};

/**
 * Process customer tagging for an order.
 * Usage: const result = await processCustomerTagging(admin, orderId, config)
 */
export const processCustomerTagging = async (
  admin: any,
  orderId: string,
  config: CustomerTagConfig
): Promise<{
  success: boolean;
  customerId?: string;
  tagsToApply?: string[];
  reason?: string;
}> => {
  // Get order and customer data
  const orderData = await getOrderAndCustomerData(admin, orderId);
  
  if (!orderData) {
    return { success: true, reason: `Order ${orderId} not found` };
  }
  
  if (!hasCustomer(orderData)) {
    return { success: true, reason: `Order ${orderId} has no associated customer` };
  }
  
  const { order } = orderData;
  const customerId = order.customer!.id;
  
  // Determine tags to apply
  const tagsToApply = determineCustomerTags(
    order.tags || [],
    order.customer!.tags || [],
    config.order_tags_and_customer_tags
  );
  
  if (tagsToApply.length === 0) {
    return { 
      success: true, 
      customerId,
      tagsToApply: [],
      reason: `No new tags to apply to customer ${customerId}` 
    };
  }
  
  return {
    success: true,
    customerId,
    tagsToApply
  };
};

/**
 * Validate customer tagging config.
 * Usage: const validation = validateCustomerTagConfig(config)
 */
export const validateCustomerTagConfig = (config: any): { isValid: boolean; error?: string } => {
  if (!config || typeof config !== 'object') {
    return { isValid: false, error: 'Config is required and must be an object' };
  }
  
  if (!config.order_tags_and_customer_tags || !Array.isArray(config.order_tags_and_customer_tags)) {
    return { isValid: false, error: 'Config must have order_tags_and_customer_tags array' };
  }
  
  if (config.order_tags_and_customer_tags.length === 0) {
    return { isValid: false, error: 'Tag mapping cannot be empty' };
  }
  
  // Validate each mapping pair
  for (const pair of config.order_tags_and_customer_tags) {
    if (!pair || typeof pair !== 'object' || !pair.key || !pair.value) {
      return { isValid: false, error: 'Each tag mapping must have key and value properties' };
    }
  }
  
  return { isValid: true };
};

/**
 * Extract customer ID from order data.
 * Usage: const customerId = extractCustomerId(orderData)
 */
export const extractCustomerId = (orderData: OrderCustomerData): string | null =>
  orderData.order.customer?.id || null;

/**
 * Create customer tagging context for logging.
 * Usage: const context = createCustomerTaggingContext(orderId, customerId, tags)
 */
export const createCustomerTaggingContext = (
  orderId: string,
  customerId?: string,
  tagsToApply?: string[]
): Record<string, any> => ({
  orderId,
  customerId,
  tagsToApply,
  tagCount: tagsToApply?.length || 0
});
