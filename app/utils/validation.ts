// 🦠 BACTERIAL VALIDATION UTILITIES
// Small, pure, self-contained validation functions

/**
 * Check if value is in allowed list.
 * Usage: isOneOf('fraud', ['customer', 'fraud', 'staff']) → true
 */
export const isOneOf = <T>(value: T, allowedValues: T[]): boolean =>
  allowedValues.includes(value);

/**
 * Validate that required fields exist in object.
 * Usage: hasRequiredFields(data, ['orderId', 'config']) → true
 */
export const hasRequiredFields = (obj: any, fields: string[]): boolean =>
  fields.every(field => obj && obj[field] !== undefined && obj[field] !== null);

/**
 * Check if string is non-empty after trimming.
 * Usage: isNonEmptyString('  hello  ') → true
 */
export const isNonEmptyString = (value: any): value is string =>
  typeof value === 'string' && value.trim().length > 0;

/**
 * Validate enum value with case-insensitive matching.
 * Usage: validateEnum('FRAUD', ['customer', 'fraud', 'staff']) → 'fraud'
 */
export const validateEnum = <T extends string>(
  value: string, 
  allowedValues: T[]
): T | null => {
  const normalized = value.toLowerCase();
  const found = allowedValues.find(v => v.toLowerCase() === normalized);
  return found || null;
};

/**
 * Create a validator function for specific enum.
 * Usage: const validateReason = createEnumValidator(['customer', 'fraud', 'staff'])
 */
export const createEnumValidator = <T extends string>(allowedValues: readonly T[]) =>
  (value: string): T | null => validateEnum(value, [...allowedValues]);

/**
 * Validate object structure matches expected shape.
 * Usage: validateShape(data, { orderId: 'string', config: 'object' })
 */
export const validateShape = (
  obj: any, 
  shape: Record<string, 'string' | 'number' | 'boolean' | 'object' | 'array'>
): boolean => {
  if (!obj || typeof obj !== 'object') return false;
  
  return Object.entries(shape).every(([key, expectedType]) => {
    const value = obj[key];
    
    switch (expectedType) {
      case 'array':
        return Array.isArray(value);
      case 'object':
        return value !== null && typeof value === 'object' && !Array.isArray(value);
      default:
        return typeof value === expectedType;
    }
  });
};

/**
 * Create validation result with error message.
 * Usage: const result = createValidationResult(false, 'Invalid data')
 */
export const createValidationResult = (isValid: boolean, error?: string) => ({
  isValid,
  error: error || null
});

/**
 * Validate multiple conditions and return first error.
 * Usage: validateAll([
 *   () => isNonEmptyString(id) || 'ID required',
 *   () => isOneOf(status, validStatuses) || 'Invalid status'
 * ])
 */
export const validateAll = (validators: (() => string | true)[]): { isValid: boolean; error: string | null } => {
  for (const validator of validators) {
    const result = validator();
    if (result !== true) {
      return createValidationResult(false, result);
    }
  }
  return createValidationResult(true);
};
