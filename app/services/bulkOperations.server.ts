import { JobType } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import prisma from "../db.server";
import { logToFile } from "../utils/logger.server";

/**
 * A map of JobTypes to their corresponding GraphQL bulk operation queries.
 */
const BULK_QUERIES: Partial<Record<JobType, string>> = {
  [JobType.AUTO_TAG_ORDERS_UTM]: `#graphql
    query {
      orders(first: 250) {
        edges {
          node {
            __typename
            id
            tags
            customerJourneySummary {
              moments(first: 250) {
                edges {
                  node {
                    __typename
                    ... on CustomerVisit {
                      utmParameters { campaign content medium source term }
                    }
                  }
                }
              }
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }`,
  [JobType.ORDER_COLLECTION_TAG]: `#graphql
    query {
      orders {
        edges {
          node {
            __typename
            id
            tags
            lineItems(first: 50) {
              edges {
                node {
                  __typename
                  id
                  product {
                    id
                    collections(first: 50) {
                      edges {
                        node {
                          __typename
                          id
                          legacyResourceId
                          title
                          handle
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }`,
  [JobType.AUTO_TAG_CUSTOMER_BY_VENDOR]: `#graphql
    query {
      orders(first: 250) {
        edges {
          node {
            __typename
            id
            customer {
              id
              tags
            }
            lineItems(first: 250) {
              edges {
                node {
                  __typename
                  vendor
                }
              }
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
        }
      }
    }`,
};

/**
 * Initiates a Shopify bulk operation for a given automation.
 *
 * @param admin The Shopify Admin API client.
 * @param automation The automation record from the database.
 * @returns A promise that resolves with an object indicating success or an error message.
 */
export async function startBulkOperation(admin: ShopifyUnAuthenticatedAdminClient, automation: { id: number; type: JobType; shop: string; }) {
  const bulkQuery = BULK_QUERIES[automation.type];

  if (!bulkQuery) {
    return { success: false, error: "This automation does not support manual bulk runs." };
  }

  try {
    const response = await admin.graphql(
      `#graphql
      mutation bulkOperationRunQuery($query: String!) {
        bulkOperationRunQuery(query: $query) {
          bulkOperation { id status }
          userErrors { field message }
        }
      }`,
      { variables: { query: bulkQuery } }
    );

    const responseJson = await response.json();
    const bulkOperation = responseJson.data?.bulkOperationRunQuery?.bulkOperation;
    const userErrors = responseJson.data?.bulkOperationRunQuery?.userErrors;
    logToFile("bulk-operations", "info", JSON.stringify(responseJson));

    if (userErrors && userErrors.length > 0) {
      const errorMessage = `Failed to start bulk operation: ${userErrors[0].message}`;
      logToFile(automation.id.toString(), "error", JSON.stringify(userErrors));
      return { success: false, error: errorMessage };
    }

    // Create a "tracker" job to monitor this bulk operation.
    await prisma.job.create({
      data: {
        shop: automation.shop,
        type: automation.type,
        data: JSON.stringify({ bulkOperationId: bulkOperation.id, automationId: automation.id }),
        status: "processing",
      }
    });

    logToFile(automation.id.toString(), "info", `Successfully started bulk operation ${bulkOperation.id}`);
    return { success: true, message: "Bulk run started successfully." };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(automation.id.toString(), "error", `Error starting bulk run: ${errorMessage}`);
    return { success: false, error: "An internal error occurred while starting the bulk run." };
  }
}