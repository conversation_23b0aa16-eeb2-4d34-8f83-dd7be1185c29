// app/queues/inventory.server.ts
import prisma from "../db.server";
import type { JobType, JobStatus } from "@prisma/client";

/**
 * Queue management for inventory-related background jobs.
 * This module handles the creation, retrieval, and status updates of jobs
 * for processing inventory updates and related automations.
 */

/**
 * Creates a new job in the database for background processing.
 * @param type - The type of job (e.g., "inventory_update")
 * @param data - The data payload for the job (e.g., inventory_item_id), as a JSON string
 * @returns The created job object
 */
export async function addJob(params: { shop: string; jobType: JobType; data: any; sessionId?: string; scheduledAt?: Date }) {
  const { shop, jobType, data, sessionId, scheduledAt } = params;
  try {
    return await prisma.job.create({
      data: {
        shop, // Add shop domain
        type: jobType,
        data: JSON.stringify(data), // Ensure data is stored as a JSON string
        sessionId,
        status: scheduledAt ? "scheduled" as JobStatus : "pending",
        retryCount: 0,
        maxRetries: 3,
        scheduledAt: scheduledAt || null,
      },
    });
  } catch (error: unknown) {
    console.error("Error creating job:", error);
    throw new Error(`Failed to create job: ${(error as Error).message}`);
  }
}

/**
 * Retrieves the next pending job from the database, ensuring concurrency control.
 * Uses a locking mechanism to prevent multiple workers from processing the same job.
 * @param workerId - Unique identifier for the worker requesting the job
 * @returns The next pending job, or null if no jobs are available
 */
export async function getNextPendingJob(workerId?: string) {
  try {
    // First, find a pending or scheduled job that is not locked or whose lock has expired (e.g., worker crashed)
    const now = new Date();
    const job = await prisma.job.findFirst({
      where: {
        status: { in: ["pending", "scheduled"] as JobStatus[] },
        // Temporarily suppress TypeScript error for scheduledAt until Prisma client fully updates
        // @ts-ignore
        OR: [
          { scheduledAt: null },
          { scheduledAt: { lte: now } }
        ],
        AND: {
          OR: [
            { lockedById: null },
            { lockedAt: null },
            { lockedAt: { lt: new Date(now.getTime() - 30 * 60000) } }, // Lock expires after 30 minutes
          ]
        }
      },
      orderBy: { createdAt: "asc" },
    });

    if (!job) return null;

    // Lock the job by updating it with the worker's ID and current timestamp
    // Attempt to lock the job, ensuring it's still pending and available.
    // Using updateMany to avoid throwing an error if the record is not found
    // or doesn't match the where clause (e.g., another worker picked it up).
    const updateResult = await prisma.job.updateMany({
      where: {
        id: job.id,
        status: { in: ["pending", "scheduled"] as JobStatus[] },
        // Temporarily suppress TypeScript error for scheduledAt until Prisma client fully updates
        // @ts-ignore
        OR: [
          { scheduledAt: null },
          { scheduledAt: { lte: now } }
        ],
        AND: {
          OR: [
            { lockedById: null },
            { lockedAt: null },
            { lockedAt: { lt: new Date(now.getTime() - 30 * 60000) } }, // Lock expires after 30 minutes
          ]
        }
      },
      data: {
        status: "processing",
        lockedById: workerId || null,
        lockedAt: now,
      },
    });

    if (updateResult.count === 0) {
      // If no records were updated, it means another worker already picked up this job
      // or it no longer met the criteria.
      return null;
    }

    // Successfully locked the job, now retrieve the updated job object to return
    return await prisma.job.findUnique({ where: { id: job.id } });
  } catch (error) {
    console.error("Failed to retrieve next pending job:", error);
    return null;
  }
}

/**
 * Updates the status of a job in the database.
 * @param jobId - The ID of the job to update
 * @param status - The new status of the job (e.g., "completed", "failed", "retrying")
 * @param errorMessage - Optional error message if the job failed
 * @param retryCount - Optional updated retry count if retrying
 * @returns The updated job object
 */
export async function updateJobStatus(
  jobId: string,
  status: JobStatus,
  errorMessage?: string,
  startedAt?: Date,
  retryCount?: number,
) {
  try {
    const data: { status: JobStatus; errorMessage?: string; startedAt?: Date; retryCount?: number; completedAt?: Date; lockedById?: string | null; lockedAt?: Date | null } = { status };
    if (errorMessage) data.errorMessage = errorMessage;
    if (startedAt) data.startedAt = startedAt;
    if (retryCount !== undefined) data.retryCount = retryCount;
    if (status === "completed") data.completedAt = new Date();
    if (status !== "processing") {
      // Release the lock if not processing
      data.lockedById = null;
      data.lockedAt = null;
    }

    return await prisma.job.update({
      where: { id: jobId },
      data,
    });
  } catch (error) {
    console.error(`Failed to update job ${jobId} status to ${status}:`, error);
    throw new Error(`Failed to update job status: ${(error as Error).message}`);
  }
}

/**
 * Retrieves a job by its ID.
 * @param jobId - The ID of the job to retrieve
 * @returns The job object, or null if not found
 */
export async function getJobById(jobId: string) {
  try {
    return await prisma.job.findUnique({
      where: { id: jobId },
    });
  } catch (error) {
    console.error(`Failed to retrieve job ${jobId}:`, error);
    return null;
  }
}

/**
 * Retrieves all jobs with a specific status.
 * @param status - The status to filter by (e.g., "pending", "processing")
 * @returns Array of job objects with the specified status
 */
export async function getJobsByStatus(status: JobStatus) {
  try {
    return await prisma.job.findMany({
      where: { status },
      orderBy: { createdAt: "desc" },
    });
  } catch (error) {
    console.error(`Failed to retrieve jobs with status ${status}:`, error);
    return [];
  }
}

/**
 * Marks a job for retry if it has not exceeded the maximum retry limit.
 * @param jobId - The ID of the job to retry
 * @returns The updated job object, or null if max retries exceeded
 */
export async function retryJob(jobId: string) {
  try {
    const job = await prisma.job.findUnique({ where: { id: jobId } });
    if (!job) return null;

    if (job.retryCount >= job.maxRetries) {
      await updateJobStatus(jobId, "failed", "Maximum retry limit reached");
      return null;
    }

    return await updateJobStatus(
      jobId,
      "pending",
      job.errorMessage ?? undefined,
      undefined, // Pass undefined for startedAt
      job.retryCount + 1
    );
  } catch (error) {
    console.error(`Failed to retry job ${jobId}:`, error);
    return null;
  }
}

/**
 * Cancels ongoing or pending jobs, typically in response to a user "Stop" action.
 * @param type - Optional job type to filter which jobs to cancel
 * @returns Number of jobs canceled
 */
export async function cancelJobs(type?: JobType) {
  try {
    const whereClause: any = { status: { in: ["pending", "processing"] } };
    if (type) whereClause.type = type;

    const updatedJobs = await prisma.job.updateMany({
      where: whereClause,
      data: {
        status: "canceled",
        errorMessage: "Job canceled by user or system action",
        lockedById: null,
        lockedAt: null,
      },
    });

    return updatedJobs.count;
  } catch (error) {
    console.error(`Failed to cancel jobs${type ? ` of type ${type}` : ""}:`, error);
    return 0;
  }
}
