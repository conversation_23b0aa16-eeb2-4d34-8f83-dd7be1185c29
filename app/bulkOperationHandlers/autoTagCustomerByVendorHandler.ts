import { logToFile } from "../utils/logger.server";
import type { BulkOperationHandler } from ".";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";

const TAGS_ADD_MUTATION = `
  mutation tagsAdd($id: ID!, $tags: [String!]!) {
    tagsAdd(id: $id, tags: $tags) {
      node {
        id
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const autoTagCustomerByVendorHandler: BulkOperationHandler = async (
  shop,
  lines,
  config,
  admin,
) => {
  const customerTags = new Map<string, Set<string>>();
  const logContext = `bulk-customer-vendor-tag-${shop}`;

  logToFile(
    logContext,
    "info",
    `Processing ${lines.length} lines for shop ${shop}`,
  );

  for (const line of lines) {
    // We only care about line items, which have a __parentId (the order)
    if (line.__parentId && line.vendor) {
      // Find the order this line item belongs to
      const order = lines.find((l) => l.id === line.__parentId);
      if (order && order.customer) {
        const customerId = order.customer.id;
        const vendorTag = `${config.prefix}${line.vendor}`;

        if (!customerTags.has(customerId)) {
          customerTags.set(customerId, new Set());
        }
        customerTags.get(customerId)!.add(vendorTag);
      }
    }
  }

  let processedCount = 0;
  for (const [customerId, tags] of customerTags.entries()) {
    try {
      const data = await shopifyGraphqlRequest(
        admin,
        TAGS_ADD_MUTATION,
        {
          id: customerId,
          tags: Array.from(tags),
        },
        logContext,
      );

      const userErrors = data.data?.tagsAdd?.userErrors;

      if (userErrors && userErrors.length > 0) {
        logToFile(
          logContext,
          "error",
          `Error tagging customer ${customerId}: ${JSON.stringify(userErrors)}`,
        );
      } else {
        processedCount++;
      }
    } catch (error: any) {
      // The error is already logged by shopifyGraphqlRequest, but we can add context
      logToFile(
        logContext,
        "error",
        `Failed to process GraphQL mutation for customer ${customerId}.`,
      );
    }
  }

  logToFile(
    logContext,
    "info",
    `Successfully processed and tagged ${processedCount} customers for shop ${shop}`,
  );

  return { processedCount };
};

