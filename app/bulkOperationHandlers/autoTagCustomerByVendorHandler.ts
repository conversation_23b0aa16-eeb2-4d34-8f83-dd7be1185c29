import { logToFile } from "../utils/logger.server";
import type { BulkOperationHandler } from ".";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import {
  createBulkLogger,
  createBulkContext,
  logBulkStep,
  createBulkResult
} from "../utils/bulk-operations";
import {
  processBulkVendorData,
  validateBulkVendorConfig,
  createBulkVendorContext,
  countVendorTags,
  type BulkVendorTaggingConfig
} from "../utils/bulk-vendor-tagging";
import { addCustomerTags } from "../utils/shopify-customers";

// 🦠 BACTERIAL FUNCTION - Execute bulk customer tagging
const executeBulkCustomerTagging = async (
  admin: any,
  customerTags: Map<string, Set<string>>,
  logger: {
    info: (context: string, message: string) => void;
    error: (context: string, message: string) => void;
  },
  logContext: string
): Promise<{ processedCount: number; errorCount: number }> => {
  let processedCount = 0;
  let errorCount = 0;

  for (const [customerId, tags] of customerTags.entries()) {
    if (tags.size > 0) {
      const tagsArray = Array.from(tags);
      logger.info(logContext, `Adding vendor tags [${tagsArray.join(', ')}] to customer ${customerId}`);

      try {
        const result = await addCustomerTags(admin, customerId, tagsArray);
        if (result.success) {
          processedCount++;
        } else {
          errorCount++;
          logger.error(logContext, `Failed to apply vendor tags to customer ${customerId}: ${JSON.stringify(result.errors)}`);
        }
      } catch (error) {
        errorCount++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error(logContext, `Failed to apply vendor tags to customer ${customerId}: ${errorMessage}`);
      }
    }
  }

  return { processedCount, errorCount };
};

export const autoTagCustomerByVendorHandler: BulkOperationHandler = async (
  shop,
  lines,
  config,
  admin,
) => {
  const logContext = createBulkContext('customer-vendor-tag', shop);
  const logger = createBulkLogger(logToFile);

  logger.info(logContext, `Starting bulk customer vendor tagging for ${lines.length} lines`);

  // 🦠 BACTERIAL APPROACH - Validate config first
  const configValidation = validateBulkVendorConfig(config);
  if (!configValidation.isValid) {
    logger.error(logContext, configValidation.error!);
    return createBulkResult(0, 1, configValidation.error);
  }

  // 🦠 BACTERIAL APPROACH - Process vendor data using focused utility
  const customerTags = processBulkVendorData(lines, config as BulkVendorTaggingConfig);

  const context = createBulkVendorContext(shop, lines.length, customerTags.size);
  const totalTags = countVendorTags(customerTags);

  logBulkStep(
    logger,
    logContext,
    'Step 1/2',
    'Processed vendor relationships',
    { ...context, totalTags }
  );

  if (customerTags.size === 0) {
    logger.info(logContext, "No customers to tag");
    return createBulkResult(0);
  }

  // 🦠 BACTERIAL APPROACH - Execute customer tagging using focused utility
  const { processedCount, errorCount } = await executeBulkCustomerTagging(
    admin,
    customerTags,
    logger,
    logContext
  );

  logBulkStep(
    logger,
    logContext,
    'Step 2/2',
    'Finished customer vendor tagging',
    { processedCount, errorCount }
  );

  return createBulkResult(processedCount, errorCount);
};

