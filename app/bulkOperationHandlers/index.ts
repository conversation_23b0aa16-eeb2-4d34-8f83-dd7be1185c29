import { JobType } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "../shopify.server";
import { handleAutoTagOrdersUtm } from "./autoTagOrdersUtmHandler";
import { handleOrderCollectionTag } from "./orderCollectionTagHandler";
import { autoTagCustomerByVendorHandler } from "./autoTagCustomerByVendorHandler";

export type BulkOperationJsonL = Record<string, any>;

export type BulkOperationHandler = (
  shop: string,
  lines: BulkOperationJsonL[],
  config: any,
  admin: ShopifyUnAuthenticatedAdminClient,
) => Promise<{ processedCount: number }>;


export const bulkOperationHandlers: Partial<Record<JobType, BulkOperationHandler>> = {
  [JobType.AUTO_TAG_ORDERS_UTM]: handleAutoTagOrdersUtm,
  [JobType.ORDER_COLLECTION_TAG]: handleOrderCollectionTag,
  [JobType.AUTO_TAG_CUSTOMER_BY_VENDOR]: autoTagCustomerByVendorHandler,
};
