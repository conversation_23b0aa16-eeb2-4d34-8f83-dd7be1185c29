import { logToFile } from "../utils/logger.server";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";
import type { BulkOperationHandler } from ".";

export const handleOrderCollectionTag: BulkOperationHandler = async (
  shop,
  lines,
  config,
  admin,
) => {
  const logContext = `bulk-order-collection-tag-${shop}`;
  logToFile(logContext, "info", `Starting inline processing for ${lines.length} lines.`);

  // 1. --- PARSE AND BUILD LOOKUP MAPS ---
  const lineItemToProductId = new Map<string, string>();
  const productToCollectionIdentifiers = new Map<string, Set<string>>();
  const orderToProductIds = new Map<string, Set<string>>();
  const orderToExistingTags = new Map<string, Set<string>>();

  // First pass: Create maps for easy lookups
  lines.forEach((line) => {
    if (line.__typename === "Order" && line.id) {
      orderToExistingTags.set(line.id, new Set(line.tags || []));
    } else if (line.__typename === "LineItem" && line.id && line.product?.id) {
      lineItemToProductId.set(line.id, line.product.id);
    }
  });

  // Second pass: Populate the main lookup tables using the maps from the first pass
  lines.forEach((line) => {
    // Connect Orders to their Products
    if (line.__typename === "LineItem" && line.__parentId) {
      const orderId = line.__parentId;
      const productId = line.product?.id;
      if (productId) {
        if (!orderToProductIds.has(orderId)) {
          orderToProductIds.set(orderId, new Set());
        }
        orderToProductIds.get(orderId)!.add(productId);
      }
    }
    // Connect Products to their Collections
    else if (line.__typename === "Collection" && line.__parentId) {
      const lineItemId = line.__parentId;
      const productId = lineItemToProductId.get(lineItemId);
      if (productId) {
        if (!productToCollectionIdentifiers.has(productId)) {
          productToCollectionIdentifiers.set(productId, new Set());
        }
        const identifiers = productToCollectionIdentifiers.get(productId)!;
        identifiers.add(line.id); // GID
        identifiers.add(line.legacyResourceId); // Legacy ID
        identifiers.add(line.title.toLowerCase()); // Title
        identifiers.add(line.handle.toLowerCase()); // Handle
      }
    }
  });

  logToFile(logContext, "info", `[Step 1/3] Aggregated data for ${orderToProductIds.size} orders.`);
  logToFile(logContext, "debug", `Order -> ProductIDs Map: ${JSON.stringify(Object.fromEntries(orderToProductIds), null, 2)}`);

  // 2. --- DETERMINE TAGS TO ADD ---
  const tagsToAddToOrder = new Map<string, Set<string>>();
  const collectionPairs = config.collections_and_tags || [];

  if (collectionPairs.length === 0) {
    logToFile(logContext, "warn", "No collection-to-tag mappings configured. Nothing to do.");
    return { processedCount: 0 };
  }

  for (const [orderId, productIds] of orderToProductIds.entries()) {
    const orderCollectionIdentifiers = new Set<string>();
    for (const productId of productIds) {
      const identifiers = productToCollectionIdentifiers.get(productId);
      if (identifiers) {
        identifiers.forEach(id => orderCollectionIdentifiers.add(id));
      }
    }

    if (orderCollectionIdentifiers.size === 0) {
      continue;
    }

    for (const pair of collectionPairs) {
      const requiredCollectionIdentifier = pair.key.toLowerCase();
      if (orderCollectionIdentifiers.has(requiredCollectionIdentifier)) {
        logToFile(logContext, "info", `Order ${orderId} matched collection '${pair.key}'.`);
        const tagsForThisCollection = pair.value.split(',').map((t: string) => t.trim()).filter(Boolean);
        const existingTags = orderToExistingTags.get(orderId) || new Set();

        for (const tag of tagsForThisCollection) {
          if (!existingTags.has(tag)) {
            if (!tagsToAddToOrder.has(orderId)) tagsToAddToOrder.set(orderId, new Set());
            tagsToAddToOrder.get(orderId)!.add(tag);
          }
        }
      }
    }
  }

  logToFile(logContext, "info", `[Step 2/3] Determined that ${tagsToAddToOrder.size} orders need new tags.`);

  // 3. --- EXECUTE TAGGING ---
  let processedCount = 0;
  for (const [orderId, tags] of tagsToAddToOrder.entries()) {
    if (tags.size > 0) {
      const tagsArray = Array.from(tags);
      logToFile(logContext, "info", `Adding tags [${tagsArray.join(', ')}] to order ${orderId}.`);
      try {
        await shopifyGraphqlRequest(admin, `#graphql
          mutation tagsAdd($id: ID!, $tags: [String!]!) {
            tagsAdd(id: $id, tags: $tags) { node { id } userErrors { field message } }
          }`, { id: orderId, tags: tagsArray }, logContext);
        processedCount++;
      } catch (e) {
        logToFile(logContext, "error", `Failed to apply tags to ${orderId}: ${e instanceof Error ? e.message : String(e)}`);
      }
    }
  }

  logToFile(logContext, "info", `[Step 3/3] Finished inline processing. Updated ${processedCount} orders.`);
  return { processedCount };
};