import { logToFile } from "../utils/logger.server";
import type { BulkOperationHandler } from ".";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import {
  createLookupMap,
  createMultiLookupMap,
  filterByType,
  mapOfSetsToObject
} from "../utils/bulk-data";
import {
  createCollectionProductMapping,
  processCollectionTagging
} from "../utils/collection-matching";
import {
  createBulkLogger,
  createBulkContext,
  logBulkStep,
  validateBulkConfig,
  executeBulkTagging,
  createBulkResult
} from "../utils/bulk-operations";

// 🦠 BACTERIAL FUNCTION - Parse bulk data into lookup maps
const parseBulkData = (lines: any[]) => {
  // Create basic lookups using bacterial utilities
  const orderLines = filterByType(lines, 'Order');
  const lineItemLines = filterByType(lines, 'LineItem');
  const collectionLines = filterByType(lines, 'Collection');

  // Order ID -> existing tags
  const orderToExistingTags = createLookupMap(
    orderLines.filter(line => line.id),
    line => line.id as string,
    line => new Set<string>(line.tags || [])
  );

  // LineItem ID -> Product ID
  const lineItemToProductId = createLookupMap(
    lineItemLines.filter(line => line.id && line.product?.id),
    line => line.id,
    line => line.product.id
  );

  // Order ID -> Product IDs (via LineItems)
  const orderToProductIds = createMultiLookupMap(
    lineItemLines.filter(line => line.__parentId && line.product?.id),
    line => line.__parentId,
    line => line.product.id
  );

  // Product ID -> Collection identifiers
  const productToCollectionIdentifiers = createCollectionProductMapping(
    collectionLines,
    lineItemToProductId
  );

  return {
    orderToExistingTags,
    lineItemToProductId,
    orderToProductIds,
    productToCollectionIdentifiers
  };
};

export const handleOrderCollectionTag: BulkOperationHandler = async (
  shop,
  lines,
  config,
  admin,
) => {
  const logContext = createBulkContext('order-collection-tag', shop);
  const logger = createBulkLogger(logToFile);

  logger.info(logContext, `Starting inline processing for ${lines.length} lines`);

  // 🦠 BACTERIAL APPROACH - Validate config first
  const configValidation = validateBulkConfig(config, ['collections_and_tags']);
  if (!configValidation.isValid) {
    logger.error(logContext, configValidation.error!);
    return createBulkResult(0, 1, configValidation.error);
  }

  const collectionPairs = config.collections_and_tags || [];
  if (collectionPairs.length === 0) {
    logger.warn(logContext, "No collection-to-tag mappings configured. Nothing to do.");
    return createBulkResult(0);
  }

  // 🦠 BACTERIAL APPROACH - Parse data using focused utilities
  const {
    orderToExistingTags,
    orderToProductIds,
    productToCollectionIdentifiers
  } = parseBulkData(lines);

  logBulkStep(
    logger,
    logContext,
    'Step 1/3',
    'Aggregated data',
    { orderCount: orderToProductIds.size }
  );

  logger.debug(logContext, `Order -> ProductIDs Map: ${JSON.stringify(mapOfSetsToObject(orderToProductIds), null, 2)}`);

  // 🦠 BACTERIAL APPROACH - Process tagging using focused utility
  const tagsToAddToOrder = processCollectionTagging(
    orderToProductIds,
    productToCollectionIdentifiers,
    collectionPairs,
    orderToExistingTags
  );

  logBulkStep(
    logger,
    logContext,
    'Step 2/3',
    'Determined tagging requirements',
    { ordersToTag: tagsToAddToOrder.size }
  );

  // 🦠 BACTERIAL APPROACH - Execute tagging using focused utility
  const { processedCount, errorCount } = await executeBulkTagging(
    admin,
    tagsToAddToOrder,
    logger,
    logContext
  );

  logBulkStep(
    logger,
    logContext,
    'Step 3/3',
    'Finished inline processing',
    { processedCount, errorCount }
  );

  return createBulkResult(processedCount, errorCount);
};