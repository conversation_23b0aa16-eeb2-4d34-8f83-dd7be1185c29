import { logToFile } from "../utils/logger.server";
import type { BulkOperationHandler } from ".";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import {
  createBulkLogger,
  createBulkContext,
  logBulkStep,
  executeBulkTagging,
  createBulkResult
} from "../utils/bulk-operations";
import {
  processBulkUtmData,
  validateBulkUtmConfig,
  createBulkUtmContext,
  countUtmTags,
  filterMomentsWithUtm
} from "../utils/bulk-utm-tagging";

export const handleAutoTagOrdersUtm: BulkOperationHandler = async (
  shop,
  lines,
  config,
  admin,
) => {
  const logContext = createBulkContext('utm-tag', shop);
  const logger = createBulkLogger(logToFile);

  logger.info(logContext, `Starting bulk UTM tagging for ${lines.length} lines`);

  // 🦠 BACTERIAL APPROACH - Validate config first
  const configValidation = validateBulkUtmConfig(config);
  if (!configValidation.isValid) {
    logger.error(logContext, configValidation.error!);
    return createBulkResult(0, 1, configValidation.error);
  }

  // 🦠 BACTERIAL APPROACH - Process UTM data using focused utility
  const orderTags = processBulkUtmData(lines);

  const utmMoments = filterMomentsWithUtm(lines);
  const context = createBulkUtmContext(shop, utmMoments.length, orderTags.size);
  const totalTags = countUtmTags(orderTags);

  logBulkStep(
    logger,
    logContext,
    'Step 1/2',
    'Processed UTM relationships',
    { ...context, totalTags }
  );

  if (orderTags.size === 0) {
    logger.info(logContext, "No orders to tag");
    return createBulkResult(0);
  }

  // 🦠 BACTERIAL APPROACH - Execute order tagging using focused utility
  const { processedCount, errorCount } = await executeBulkTagging(
    admin,
    orderTags,
    logger,
    logContext
  );

  logBulkStep(
    logger,
    logContext,
    'Step 2/2',
    'Finished UTM tagging',
    { processedCount, errorCount }
  );

  return createBulkResult(processedCount, errorCount);
};
