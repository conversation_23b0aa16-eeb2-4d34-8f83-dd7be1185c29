import { logToFile } from "../utils/logger.server";
import type { BulkOperationHandler } from ".";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";

const TAGS_ADD_MUTATION = `
  mutation tagsAdd($id: ID!, $tags: [String!]!) {
    tagsAdd(id: $id, tags: $tags) {
      node {
        id
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const handleAutoTagOrdersUtm: BulkOperationHandler = async (
  shop,
  lines,
  config,
  admin,
) => {
  const orderTags = new Map<string, Set<string>>();
  const logContext = `bulk-utm-tag-${shop}`;

  logToFile(
    logContext,
    "info",
    `Processing ${lines.length} lines for shop ${shop}`,
  );

  for (const line of lines) {
    // We only care about moments with UTM params, which have a __parentId (the order)
    if (line.__parentId && line.utmParameters) {
      const orderId = line.__parentId;
      const utmTags = Object.entries(line.utmParameters)
        .filter(([key, value]) => key && value)
        .map(([key, value]) => `${config.prefix}${key}_${value}`);

      if (utmTags.length > 0) {
        if (!orderTags.has(orderId)) {
          orderTags.set(orderId, new Set());
        }
        utmTags.forEach((tag) => orderTags.get(orderId)!.add(tag));
      }
    }
  }

  let processedCount = 0;
  for (const [orderId, tags] of orderTags.entries()) {
    try {
      const data = await shopifyGraphqlRequest(
        admin,
        TAGS_ADD_MUTATION,
        {
          id: orderId,
          tags: Array.from(tags),
        },
        logContext,
      );

      const userErrors = data.data?.tagsAdd?.userErrors;

      if (userErrors && userErrors.length > 0) {
        logToFile(
          logContext,
          "error",
          `Error tagging order ${orderId}: ${JSON.stringify(userErrors)}`,
        );
      } else {
        processedCount++;
      }
    } catch (error: any) {
      logToFile(
        logContext,
        "error",
        `Failed to process GraphQL mutation for order ${orderId}.`,
      );
    }
  }

  logToFile(
    logContext,
    "info",
    `Successfully processed and tagged ${processedCount} orders for shop ${shop}`,
  );

  return { processedCount };
};
