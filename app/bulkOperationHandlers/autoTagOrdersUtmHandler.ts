import { logToFile } from "../utils/logger.server";
import type { BulkOperationHandler } from ".";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import {
  createBulkLogger,
  createBulkContext,
  logBulkStep,
  executeBulkTagging,
  createBulkResult
} from "../utils/bulk-operations";
import {
  processBulkUtmData,
  validateBulkUtmConfig,
  createBulkUtmContext,
  countUtmTags,
  filterMomentsWithUtm,
  type BulkUtmTaggingConfig
} from "../utils/bulk-utm-tagging";

export const handleAutoTagOrdersUtm: BulkOperationHandler = async (
  shop,
  lines,
  config,
  admin,
) => {
  const logContext = createBulkContext('utm-tag', shop);
  const logger = createBulkLogger(logToFile);

  logger.info(logContext, `Starting bulk UTM tagging for ${lines.length} lines`);

  // 🦠 BACTERIAL APPROACH - Validate config first
  const configValidation = validateBulkUtmConfig(config);
  if (!configValidation.isValid) {
    logger.error(logContext, configValidation.error!);
    return createBulkResult(0, 1, configValidation.error);
  }

  // 🦠 BACTERIAL APPROACH - Process UTM data using focused utility
  const orderTags = processBulkUtmData(lines, config as BulkUtmTaggingConfig);

  const utmMoments = filterMomentsWithUtm(lines);
  const context = createBulkUtmContext(shop, utmMoments.length, orderTags.size);
  const totalTags = countUtmTags(orderTags);

  logBulkStep(
    logger,
    logContext,
    'Step 1/2',
    'Processed UTM relationships',
    { ...context, totalTags }
  );

  let processedCount = 0;
  for (const [orderId, tags] of orderTags.entries()) {
    try {
      const data = await shopifyGraphqlRequest(
        admin,
        TAGS_ADD_MUTATION,
        {
          id: orderId,
          tags: Array.from(tags),
        },
        logContext,
      );

      const userErrors = data.data?.tagsAdd?.userErrors;

      if (userErrors && userErrors.length > 0) {
        logToFile(
          logContext,
          "error",
          `Error tagging order ${orderId}: ${JSON.stringify(userErrors)}`,
        );
      } else {
        processedCount++;
      }
    } catch (error: any) {
      logToFile(
        logContext,
        "error",
        `Failed to process GraphQL mutation for order ${orderId}.`,
      );
    }
  }

  logToFile(
    logContext,
    "info",
    `Successfully processed and tagged ${processedCount} orders for shop ${shop}`,
  );

  return { processedCount };
};
