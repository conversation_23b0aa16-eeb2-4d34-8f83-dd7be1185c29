import { Button, InlineStack, Text } from "@shopify/polaris";
import { ChevronLeftIcon, ChevronRightIcon } from "@shopify/polaris-icons";
import { generatePaginationRange } from "../utils/paginationUtils";

interface NumberedPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  pageNeighbours?: number;
}

const DOTS = '...';

export function NumberedPagination({
  currentPage,
  totalPages,
  onPageChange,
  pageNeighbours = 1,
}: NumberedPaginationProps) {
  if (totalPages <= 1) {
    return null;
  }

  const pageNumbers = generatePaginationRange(currentPage, totalPages, pageNeighbours);

  return (
    <InlineStack gap="100" align="center">
      <Button
        icon={ChevronLeftIcon}
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        accessibilityLabel="Previous page"
      />

      {pageNumbers.map((page, index) => {
        if (page === DOTS) {
          return (
            <span key={`${DOTS}-${index}`} style={{ padding: '0 var(--p-space-100)' }}>
              <Text as="span" tone="subdued">{DOTS}</Text>
            </span>
          );
        }

        return (
          <Button
            key={page}
            onClick={() => {
              if (typeof page === 'number') {
                onPageChange(page);
              }
            }}
            pressed={currentPage === page}
            disabled={currentPage === page}
            aria-current={currentPage === page ? "page" : undefined}
          >
            {page.toString()}
          </Button>
        );
      })}

      <Button
        icon={ChevronRightIcon}
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        accessibilityLabel="Next page"
      />
    </InlineStack>
  );
}