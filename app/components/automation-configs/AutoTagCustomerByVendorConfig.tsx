import React from 'react';
import type { AutoTagCustomerByVendorConfig, AutomationConfigProps } from './types';

export function AutoTagCustomerByVendorConfig({ config, onConfigChange }: AutomationConfigProps<AutoTagCustomerByVendorConfig>) {
  // This automation does not require any specific configuration from the user.
  // The component can simply render a descriptive message.
  return (
    <p>This automation automatically tags customers with the vendors they have purchased from.</p>
  );
}
