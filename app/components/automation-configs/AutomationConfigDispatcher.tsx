import { JobType } from "@prisma/client";
import { Text } from "@shopify/polaris";
import { AutoTagOrdersUtmConfig } from "./AutoTagOrdersUtmConfig";
import { OrderCollectionTagConfig } from "./OrderCollectionTagConfig";
import { DefaultConfig } from "./DefaultConfig";
import { OrderCartAttributeTagConfig } from "./OrderCartAttributeTagConfig";
import { AutoTagCustomerByOrderTagConfig } from "./AutoTagCustomerByOrderTagConfig";
import { AutoTagCustomerByVendorConfig } from "./AutoTagCustomerByVendorConfig";
import { OrderDiscountTagConfig } from "./OrderDiscountTagConfig";
import { CancelHighRiskOrderConfig } from "./CancelHighRiskOrderConfig";

interface DispatcherProps {
  type: JobType;
  config: any;
  onConfigChange: (newConfig: any) => void;
}

export function AutomationConfigDispatcher({ type, config, onConfigChange }: DispatcherProps) {
  if (type === JobType.AUTO_TAG_ORDERS_UTM) {
    return <AutoTagOrdersUtmConfig config={config} onConfigChange={onConfigChange} />;
  } else if (type === JobType.ORDER_COLLECTION_TAG) {
    return <OrderCollectionTagConfig config={config} onConfigChange={onConfigChange} />;
  } else if (type === JobType.ORDER_CART_ATTRIBUTE_TAG) {
    return <OrderCartAttributeTagConfig config={config} onConfigChange={onConfigChange} />;
  } else if (type === JobType.AUTO_TAG_CUSTOMER_BY_ORDER_TAG) {
    return <AutoTagCustomerByOrderTagConfig config={config} onConfigChange={onConfigChange} />;
  } else if (type === JobType.AUTO_TAG_CUSTOMER_BY_VENDOR) {
    return <AutoTagCustomerByVendorConfig config={config} onConfigChange={onConfigChange} />;
  } else if (type === JobType.ORDER_DISCOUNT_TAG) {
    return <OrderDiscountTagConfig config={config} onConfigChange={onConfigChange} />;
  }
  else if (type === JobType.CANCEL_HIGH_RISK_ORDER) {
    return <CancelHighRiskOrderConfig config={config} onConfigChange={onConfigChange} />;
  }
  else if (type === JobType.COLLECTION_VISIBILITY_UPDATE) {
    return <DefaultConfig />;
  } else {
    return <Text as="p" tone="critical">Error: Unknown automation type. No configuration available.</Text>;
  }
}