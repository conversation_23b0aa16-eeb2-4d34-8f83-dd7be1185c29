import { Checkbox, BlockStack } from "@shopify/polaris";
import type { AutomationConfigProps } from "./types";

/**
 * Defines the configuration for UTM tagging automation.
 * Each property indicates whether a specific UTM parameter should be used for tagging.
 */
interface UtmConfig {
  tag_with_utm_campaign: boolean;
  tag_with_utm_source: boolean;
  tag_with_utm_medium: boolean;
  tag_with_utm_content: boolean;
  tag_with_utm_term: boolean;
}

export function AutoTagOrdersUtmConfig({ config, onConfigChange }: AutomationConfigProps<UtmConfig>) {
  // Helper function to reduce boilerplate
  const handleCheckboxChange = (key: keyof UtmConfig, checked: boolean) => {
    onConfigChange({ ...config, [key]: checked });
  };

  return (
    <BlockStack gap="400">
      <Checkbox
        label="Tag with UTM Campaign"
        checked={config.tag_with_utm_campaign}
        onChange={(checked) => handleCheckboxChange("tag_with_utm_campaign", checked)}
      />
      <Checkbox
        label="Tag with UTM Source"
        checked={config.tag_with_utm_source}
        onChange={(checked) => handleCheckboxChange("tag_with_utm_source", checked)}
      />
      <Checkbox
        label="Tag with UTM Medium"
        checked={config.tag_with_utm_medium}
        onChange={(checked) => handleCheckboxChange("tag_with_utm_medium", checked)}
      />
      <Checkbox
        label="Tag with UTM Content"
        checked={config.tag_with_utm_content}
        onChange={(checked) => handleCheckboxChange("tag_with_utm_content", checked)}
      />
      <Checkbox
        label="Tag with UTM Term"
        checked={config.tag_with_utm_term}
        onChange={(checked) => handleCheckboxChange("tag_with_utm_term", checked)}
      />
    </BlockStack>
  );
}