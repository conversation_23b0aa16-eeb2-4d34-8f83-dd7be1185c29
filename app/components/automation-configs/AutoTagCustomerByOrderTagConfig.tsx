import { <PERSON><PERSON>ield, Button, InlineStack, BlockStack, Text } from "@shopify/polaris";
import { DeleteIcon } from "@shopify/polaris-icons";
import type { AutomationConfigProps } from "./types";
import { useState, useEffect, useCallback, useRef } from "react";

interface OrderCustomerTagPair {
  id: string;
  key: string;
  value: string;
}

interface OrderCustomerTagConfig {
  order_tags_and_customer_tags: Omit<OrderCustomerTagPair, 'id'>[];
}

// Helper function to compare arrays of pairs (without IDs)
const areArraysEqual = (
  a: Omit<OrderCustomerTagPair, 'id'>[],
  b: Omit<OrderCustomerTagPair, 'id'>[]
): boolean => {
  if (a.length !== b.length) return false;
  return a.every((item, index) => 
    item.key === b[index].key && 
    item.value === b[index].value
  );
};

export function AutoTagCustomerByOrderTagConfig({ config, onConfigChange }: AutomationConfigProps<OrderCustomerTagConfig>) {
  const nextId = useRef(0);
  const [pairs, setPairs] = useState<OrderCustomerTagPair[]>([]);
  const pairsWithoutIdsRef = useRef<Omit<OrderCustomerTagPair, 'id'>[]>([]);

  // Keep ref updated with current pairs (without IDs)
  useEffect(() => {
    pairsWithoutIdsRef.current = pairs.map(({ id, ...rest }) => rest);
  }, [pairs]);

  // Sync state with props
  useEffect(() => {
    const incoming = config?.order_tags_and_customer_tags ?? [];
    
    // Only update if incoming data is different
    if (!areArraysEqual(incoming, pairsWithoutIdsRef.current)) {
      const newPairs = incoming.length > 0
        ? incoming.map(p => ({ ...p, id: `pair-${nextId.current++}` }))
        : [{ id: `pair-${nextId.current++}`, key: "", value: "" }];
      
      setPairs(newPairs);
    }
  }, [config?.order_tags_and_customer_tags]);

  const handleAddPair = useCallback(() => {
    const lastPair = pairs[pairs.length - 1];
    
    // Prevent adding empty rows consecutively
    if (lastPair && !lastPair.key.trim() && !lastPair.value.trim()) {
      return;
    }

    const newPair = {
      id: `pair-${nextId.current++}`,
      key: "",
      value: ""
    };

    const newPairs = [...pairs, newPair];
    setPairs(newPairs);
    onConfigChange({
      order_tags_and_customer_tags: newPairs.map(({ id, ...rest }) => rest)
    });
  }, [pairs, onConfigChange]);

  const handleRemovePair = useCallback((idToRemove: string) => {
    const newPairs = pairs.filter(p => p.id !== idToRemove);
    const finalPairs = newPairs.length > 0 
      ? newPairs 
      : [{ id: `pair-${nextId.current++}`, key: "", value: "" }];

    setPairs(finalPairs);
    onConfigChange({
      order_tags_and_customer_tags: finalPairs.map(({ id, ...rest }) => rest)
    });
  }, [pairs, onConfigChange]);

  const handlePairChange = useCallback((idToChange: string, field: 'key' | 'value', newValue: string) => {
    const newPairs = pairs.map(pair => 
      pair.id === idToChange 
        ? { ...pair, [field]: newValue } 
        : pair
    );
    
    setPairs(newPairs);
    onConfigChange({
      order_tags_and_customer_tags: newPairs.map(({ id, ...rest }) => rest)
    });
  }, [pairs, onConfigChange]);

  return (
    <BlockStack gap="400">
      <Text variant="headingMd" as="h3">Order Tags and Customer Tags Mapping</Text>
      <Text as="p" tone="subdued">
        For each row, specify an order tag on the left and the corresponding customer tag to apply on the right.
      </Text>
      <BlockStack gap="300">
        {pairs.map((pair, index) => (
          <InlineStack key={pair.id} gap="200" blockAlign="center" wrap={false}>
            <div style={{ flex: 1 }}>
              <TextField
                label="Order Tag"
                value={pair.key}
                onChange={(value) => handlePairChange(pair.id, "key", value)}
                placeholder="e.g., 'VIP'"
                autoComplete="off"
                helpText={index === 0 ? undefined : " "} // Maintain consistent height
              />
            </div>
            <div style={{ flex: 1 }}>
              <TextField
                label="Customer Tag to Apply"
                value={pair.value}
                onChange={(value) => handlePairChange(pair.id, "value", value)}
                placeholder="e.g., 'loyal-customer'"
                autoComplete="off"
                helpText={index === 0 ? undefined : " "} // Maintain consistent height
              />
            </div>
            <Button
              icon={DeleteIcon}
              accessibilityLabel={`Remove row ${index + 1}`}
              onClick={() => handleRemovePair(pair.id)}
              disabled={pairs.length === 1 && !pair.key && !pair.value}
            />
          </InlineStack>
        ))}
      </BlockStack>
      <Button onClick={handleAddPair}>Add Row</Button>
    </BlockStack>
  );
}
