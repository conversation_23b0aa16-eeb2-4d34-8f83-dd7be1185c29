import { TextField } from "@shopify/polaris";
import type { AutomationConfigProps } from "./types";

/**
 * Defines the configuration for the cart attribute tagging automation.
 */
interface CartAttributeConfig {
  cart_attribute_to_monitor: string;
}

export function OrderCartAttributeTagConfig({ config, onConfigChange }: AutomationConfigProps<CartAttributeConfig>) {
  
  const handleAttributeChange = (value: string) => {
    onConfigChange({ ...config, cart_attribute_to_monitor: value });
  };

  return (
    <TextField
      label="Cart Attribute to Monitor"
      value={config.cart_attribute_to_monitor || ""}
      onChange={handleAttributeChange}
      helpText="The name of the cart note attribute whose value you want to use as a tag (e.g., 'referral_source')."
      autoComplete="off"
    />
  );
}