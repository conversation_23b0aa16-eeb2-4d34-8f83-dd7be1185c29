// app/components/automation-configs/CancelHighRiskOrderConfig.tsx

import { BlockStack, Checkbox, TextField } from "@shopify/polaris";
import type { AutomationConfigProps } from "./types";

interface CancelHighRiskOrderConfig {
  cancellation_reason_to_set: string;
  ignore_unpaid_orders: boolean;
  refund_payment_for_cancelled_orders: boolean;
  restock_inventory_for_cancelled_orders: boolean;
  email_customer_when_cancelling: boolean;
  staff_note_for_timeline: string;
  add_this_order_tag_when_cancelling: string;
}

export function CancelHighRiskOrderConfig({ config, onConfigChange }: AutomationConfigProps<CancelHighRiskOrderConfig>) {
  const handleConfigChange = (key: keyof CancelHighRiskOrderConfig, value: string | boolean) => {
    onConfigChange({ ...config, [key]: value });
  };

  return (
    <BlockStack gap="400">
      <TextField
        label="Cancellation reason to set"
        value={config.cancellation_reason_to_set}
        onChange={(value) => handleConfigChange("cancellation_reason_to_set", value)}
        helpText="Must be one of: customer, declined, fraud, inventory, other, staff."
        autoComplete="off"
      />
      <TextField
        label="Staff note for timeline"
        value={config.staff_note_for_timeline}
        onChange={(value) => handleConfigChange("staff_note_for_timeline", value)}
        autoComplete="off"
      />
      <TextField
        label="Add this order tag when cancelling"
        value={config.add_this_order_tag_when_cancelling}
        onChange={(value) => handleConfigChange("add_this_order_tag_when_cancelling", value)}
        autoComplete="off"
      />
      <Checkbox
        label="Ignore unpaid orders"
        checked={config.ignore_unpaid_orders}
        onChange={(checked) => handleConfigChange("ignore_unpaid_orders", checked)}
      />
      <Checkbox
        label="Refund payment for cancelled orders"
        checked={config.refund_payment_for_cancelled_orders}
        onChange={(checked) => handleConfigChange("refund_payment_for_cancelled_orders", checked)}
      />
      <Checkbox
        label="Restock inventory for cancelled orders"
        checked={config.restock_inventory_for_cancelled_orders}
        onChange={(checked) => handleConfigChange("restock_inventory_for_cancelled_orders", checked)}
      />
      <Checkbox
        label="Email customer when cancelling"
        checked={config.email_customer_when_cancelling}
        onChange={(checked) => handleConfigChange("email_customer_when_cancelling", checked)}
      />
    </BlockStack>
  );
}