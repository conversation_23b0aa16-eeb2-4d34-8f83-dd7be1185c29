import { <PERSON><PERSON>ield, Button, InlineStack, BlockStack, Text } from "@shopify/polaris";
import { DeleteIcon } from "@shopify/polaris-icons";
import type { AutomationConfigProps } from "./types";
import { useState, useEffect, useCallback, useRef } from "react";

interface CollectionTagPair {
  id: string;
  key: string;
  value: string;
}

interface CollectionTagConfig {
  collections_and_tags: Omit<CollectionTagPair, 'id'>[];
}

// Helper function to compare arrays of pairs (without IDs)
const areArraysEqual = (
  a: Omit<CollectionTagPair, 'id'>[],
  b: Omit<CollectionTagPair, 'id'>[]
): boolean => {
  if (a.length !== b.length) return false;
  return a.every((item, index) => 
    item.key === b[index].key && 
    item.value === b[index].value
  );
};

export function OrderCollectionTagConfig({ config, onConfigChange }: AutomationConfigProps<CollectionTagConfig>) {
  const nextId = useRef(0);
  const [pairs, setPairs] = useState<CollectionTagPair[]>([]);
  const pairsWithoutIdsRef = useRef<Omit<CollectionTagPair, 'id'>[]>([]);

  // Keep ref updated with current pairs (without IDs)
  useEffect(() => {
    pairsWithoutIdsRef.current = pairs.map(({ id, ...rest }) => rest);
  }, [pairs]);

  // Sync state with props
  useEffect(() => {
    const incoming = config?.collections_and_tags ?? [];
    
    // Only update if incoming data is different
    if (!areArraysEqual(incoming, pairsWithoutIdsRef.current)) {
      const newPairs = incoming.length > 0
        ? incoming.map(p => ({ ...p, id: `pair-${nextId.current++}` }))
        : [{ id: `pair-${nextId.current++}`, key: "", value: "" }];
      
      setPairs(newPairs);
    }
  }, [config?.collections_and_tags]);

  const handleAddPair = useCallback(() => {
    const lastPair = pairs[pairs.length - 1];
    
    // Prevent adding empty rows consecutively
    if (lastPair && !lastPair.key.trim() && !lastPair.value.trim()) {
      return;
    }

    const newPair = {
      id: `pair-${nextId.current++}`,
      key: "",
      value: ""
    };

    const newPairs = [...pairs, newPair];
    setPairs(newPairs);
    onConfigChange({
      collections_and_tags: newPairs.map(({ id, ...rest }) => rest)
    });
  }, [pairs, onConfigChange]);

  const handleRemovePair = useCallback((idToRemove: string) => {
    const newPairs = pairs.filter(p => p.id !== idToRemove);
    const finalPairs = newPairs.length > 0 
      ? newPairs 
      : [{ id: `pair-${nextId.current++}`, key: "", value: "" }];

    setPairs(finalPairs);
    onConfigChange({
      collections_and_tags: finalPairs.map(({ id, ...rest }) => rest)
    });
  }, [pairs, onConfigChange]);

  const handlePairChange = useCallback((idToChange: string, field: 'key' | 'value', newValue: string) => {
    const newPairs = pairs.map(pair => 
      pair.id === idToChange 
        ? { ...pair, [field]: newValue } 
        : pair
    );
    
    setPairs(newPairs);
    onConfigChange({
      collections_and_tags: newPairs.map(({ id, ...rest }) => rest)
    });
  }, [pairs, onConfigChange]);

  return (
    <BlockStack gap="400">
      <Text variant="headingMd" as="h3">Collections and Tags Mapping</Text>
      <Text as="p" tone="subdued">
        For each row, specify a collection identifier on the left and the comma-separated tags you want to apply on the right.
      </Text>
      <BlockStack gap="300">
        {pairs.map((pair, index) => (
          <InlineStack key={pair.id} gap="200" blockAlign="center" wrap={false}>
            <div style={{ flex: 1 }}>
              <TextField
                label="Collection Identifier (Title, Handle, or ID)"
                value={pair.key}
                onChange={(value) => handlePairChange(pair.id, "key", value)}
                placeholder="e.g., 'Best Sellers' or 'best-sellers'"
                autoComplete="off"
                helpText={index === 0 ? undefined : " "} // Maintain consistent height
              />
            </div>
            <div style={{ flex: 1 }}>
              <TextField
                label="Tags to Apply (comma-separated)"
                value={pair.value}
                onChange={(value) => handlePairChange(pair.id, "value", value)}
                placeholder="e.g., vip, special-order"
                autoComplete="off"
                helpText={index === 0 ? undefined : " "} // Maintain consistent height
              />
            </div>
            <Button
              icon={DeleteIcon}
              accessibilityLabel={`Remove row ${index + 1}`}
              onClick={() => handleRemovePair(pair.id)}
              disabled={pairs.length === 1 && !pair.key && !pair.value}
            />
          </InlineStack>
        ))}
      </BlockStack>
      <Button onClick={handleAddPair}>Add Row</Button>
    </BlockStack>
  );
}