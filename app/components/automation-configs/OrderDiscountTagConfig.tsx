import { BlockStack, Text } from "@shopify/polaris";
import type { AutomationConfigProps } from "./types";

interface OrderDiscountTagConfigType {
  // No specific configuration options for this automation yet
}

export function OrderDiscountTagConfig({ config, onConfigChange }: AutomationConfigProps<OrderDiscountTagConfigType>) {
  return (
    <BlockStack gap="400">
      <Text as="p" variant="bodyMd">
        This automation does not require any specific configuration.
      </Text>
    </BlockStack>
  );
}
