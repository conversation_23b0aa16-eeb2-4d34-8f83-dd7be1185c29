import { JobType } from "@prisma/client";

// This represents the master list of all automations your app provides.
// In a more advanced app, this could come from a database or a config file.
export const TASKS_LIBRARY = [
  {
    id: 1,
    title: "Manage Collection Visibility on Inventory Change",
    description: "Automatically publish or unpublish collections when product inventory changes to prevent empty collections.",
    category: "products",
    type: JobType.COLLECTION_VISIBILITY_UPDATE,
    trigger: "When inventory is updated", // Add trigger here as it's part of the template
    optionsSchema: undefined, // Explicitly define it as undefined for this task
  },
  // Add other pre-built tasks here in the future
  {
    id: 2,
    title: "Auto-tag Orders with UTM Parameters",
    description: "Automatically adds tags to orders based on the UTM parameters (campaign, source, medium, content, term) captured during the customer's journey.",
    category: "orders",
    type: JobType.AUTO_TAG_ORDERS_UTM,
    trigger: "Order Creation/Update, Manual Bulk Run",
    optionsSchema: [
      { name: "tag_with_utm_campaign", label: "Tag with UTM Campaign", type: "boolean", defaultValue: true },
      { name: "tag_with_utm_source", label: "Tag with UTM Source", type: "boolean", defaultValue: true },
      { name: "tag_with_utm_medium", label: "Tag with UTM Medium", type: "boolean", defaultValue: true },
      { name: "tag_with_utm_content", label: "Tag with UTM Content", type: "boolean", defaultValue: false },
      { name: "tag_with_utm_term", label: "Tag with UTM Term", type: "boolean", defaultValue: false },
    ],
  },
  {
    id: 3,
    title: "Tag Orders by Collection",
    description: "Automatically adds tags to orders based on the collection(s) the customer has ordered from. Useful for broadly classifying orders.",
    category: "orders",
    type: JobType.ORDER_COLLECTION_TAG,
    trigger: "Order Creation, Manual Bulk Run",
    optionsSchema: [
      {
        name: "collections_and_tags",
        label: "Collections and Tags Mapping",
        type: "keyvalue",
        defaultValue: [{ key: "", value: "" }],
        helpText: "Left: Collection Title, Handle, or ID. Right: Comma-separated tags to apply."
      }
    ]
  },
  {
    id: 4, // Ensure this ID is unique
    title: "Tag Order with Cart Attribute",
    description: "Checks for a specific cart note attribute on an order and adds its value as a tag. Useful for tracking referrals, special instructions, or other metadata.",
    category: "orders",
    type: JobType.ORDER_CART_ATTRIBUTE_TAG,
    trigger: "Order Creation/Update, Manual Bulk Run",
    optionsSchema: [
      {
        name: "cart_attribute_to_monitor",
        label: "Cart Attribute to Monitor",
        type: "text",
        defaultValue: "",
        helpText: "The name of the cart note attribute whose value you want to use as a tag (e.g., 'referral_source')."
      }
    ]
  },
  {
    id: 5,
    title: "Tag Customer Based on Order Tags",
    description: "Watches for specific tags on new or updated orders and applies corresponding tags to the customer.",
    category: "customers",
    type: JobType.AUTO_TAG_CUSTOMER_BY_ORDER_TAG,
    trigger: "Order Creation/Update",
    optionsSchema: [
      {
        name: "order_tags_and_customer_tags",
        label: "Order Tags and Customer Tags Mapping",
        type: "keyvalue",
        defaultValue: [{ key: "", value: "" }],
        helpText: "Left: Order Tag. Right: Customer Tag to apply."
      }
    ]
  },
  {
    id: 6,
    title: "Auto-tag Customer by Vendor",
    description: "Automatically tags customers with the names of vendors from whom they have purchased products.",
    category: "customers",
    type: JobType.AUTO_TAG_CUSTOMER_BY_VENDOR,
    trigger: "Order Creation, Manual Bulk Run",
    optionsSchema: undefined,
  },
  {
    id: 7,
    title: "Tag Orders with Discount Codes",
    description: "Automatically tags orders with any discount codes that were applied.",
    category: "orders",
    type: JobType.ORDER_DISCOUNT_TAG,
    trigger: "Order Creation",
    optionsSchema: undefined,
  },
  {
    id: 8,
    title: "Cancel High-Risk Orders",
    description: "Automatically cancels orders as soon as Shopify (or another risk-analysis app) determines it to be high risk. Optionally, this task can also auto-tag the order, email the customer, restock the inventory, and/or refund payment.",
    category: "orders",
    type: JobType.CANCEL_HIGH_RISK_ORDER,
    trigger: "Order Creation/Update",
    optionsSchema: [
      {
        name: "cancellation_reason_to_set",
        label: "Cancellation reason to set",
        type: "text",
        defaultValue: "fraud",
        helpText: "Must be one of: customer, declined, fraud, inventory, other, staff."
      },
      {
        name: "ignore_unpaid_orders",
        label: "Ignore unpaid orders",
        type: "boolean",
        defaultValue: false,
      },
      {
        name: "refund_payment_for_cancelled_orders",
        label: "Refund payment for cancelled orders",
        type: "boolean",
        defaultValue: true,
      },
      {
        name: "restock_inventory_for_cancelled_orders",
        label: "Restock inventory for cancelled orders",
        type: "boolean",
        defaultValue: true,
      },
      {
        name: "email_customer_when_cancelling",
        label: "Email customer when cancelling",
        type: "boolean",
        defaultValue: true,
      },
      {
        name: "staff_note_for_timeline",
        label: "Staff note for timeline",
        type: "text",
        defaultValue: "Order automatically canceled due to high fraud risk.",
      },
      {
        name: "add_this_order_tag_when_cancelling",
        label: "Add this order tag when cancelling",
        type: "text",
        defaultValue: "fraud",
      },
    ],
  }
];