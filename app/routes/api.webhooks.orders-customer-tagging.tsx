import type { ActionFunctionArgs } from "@remix-run/node";
import { JobType, JobStatus } from "@prisma/client"; // Import JobStatus
import { authenticate } from "../shopify.server";
import { createAutomationJob } from "../services/automations.server";
import { logToFile } from "../utils/logger.server";
import prisma from "../db.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { topic, shop, admin, payload } = await authenticate.webhook(request);

  if (!admin) {
    throw new Response("Unauthorized", { status: 401 });
  }

  switch (topic) {
    case "ORDERS_CREATE":
    case "ORDERS_UPDATED":
      logToFile("webhook-orders-customer-tagging", "info", `Received ${topic} webhook for shop: ${shop}`);
      try {
        const activeAutomation = await prisma.automation.findFirst({
          where: {
            shop,
            type: JobType.AUTO_TAG_CUSTOMER_BY_ORDER_TAG,
            status: "Active",
          },
        });

        if (!activeAutomation) {
          logToFile("webhook-orders-customer-tagging", "info", `No active AUTO_TAG_CUSTOMER_BY_ORDER_TAG automation found for shop ${shop}. Skipping job creation.`);
          return new Response("Automation not active", { status: 200 });
        }

        const orderGid = payload.admin_graphql_api_id;
        if (!orderGid) {
            logToFile("webhook-orders-customer-tagging", "error", `Missing admin_graphql_api_id in webhook payload for shop: ${shop}`);
            return new Response("Missing admin_graphql_api_id", { status: 400 });
        }

        // --- DE-DUPLICATION LOGIC ---
        // Check if a pending or processing job for this specific order already exists.
        const existingJob = await prisma.job.findFirst({
          where: {
            shop,
            type: JobType.AUTO_TAG_CUSTOMER_BY_ORDER_TAG,
            data: {
              contains: `"${orderGid}"`,
            },
            status: {
              in: [JobStatus.pending, JobStatus.processing, JobStatus.scheduled],
            },
          },
        });

        if (existingJob) {
          logToFile("webhook-orders-customer-tagging", "info", `Skipping duplicate job enqueue for order ID: ${orderGid}. A job already exists with status: ${existingJob.status}.`);
          return new Response("Duplicate job request", { status: 200 });
        }
        // --- END DE-DUPLICATION LOGIC ---

        // The job handler will fetch all necessary data. We only need to pass the ID and config.
        const jobData = {
          orderId: orderGid,
          config: activeAutomation.config ?? {},
        };

        await createAutomationJob(shop, JobType.AUTO_TAG_CUSTOMER_BY_ORDER_TAG, jobData);

        logToFile("webhook-orders-customer-tagging", "info", `Successfully enqueued AUTO_TAG_CUSTOMER_BY_ORDER_TAG job for order ID: ${payload.id} for shop: ${shop}`);
        return new Response("Job enqueued successfully", { status: 200 });

      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logToFile("webhook-orders-customer-tagging", "error", `Failed to enqueue job for ${topic} webhook for shop: ${shop}: ${errorMessage}`);
        return new Response("Failed to enqueue job", { status: 500 });
      }

    default:
      logToFile("webhook-orders-customer-tagging", "warn", `Unhandled webhook topic: ${topic} for shop: ${shop}`);
      throw new Response("Unhandled webhook topic", { status: 404 });
  }
};