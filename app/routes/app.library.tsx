import { type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, useFetcher, useLoaderData, useSearchParams, useSubmit, Link as RemixLink } from "@remix-run/react";
import { <PERSON>ton, Card, Grid, Layout, Page, Text, TextField, Tabs, BlockStack, InlineStack } from "@shopify/polaris";
// Removed import { Link } from "@remix-run/react"; as it's now RemixLink to avoid confusion if Polaris had a Link, though it's not used directly here.
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { useEffect, useMemo } from "react";
import type { JobType, Prisma } from "@prisma/client";
import { TASKS_LIBRARY } from "../data/tasks";
import { NumberedPagination } from "../components/NumberedPagination"; // Import pagination component

const TABS = [
    { id: 'all', content: 'All' },
    { id: 'products', content: 'Products' },
    { id: 'orders', content: 'Orders' },
    { id: 'customers', content: 'Customers' },
];

const PAGE_SIZE = 9; // Number of tasks per page, suitable for a 3-column grid

// Define the shape of a task as it's used in the component and returned by the loader
type DisplayTask = {
  id: number;
  title: string;
  description: string;
  category: string;
  type: JobType;
  trigger: string;
};

// Define the overall shape of the data returned by the loader
type LoaderData = {
  tasks: DisplayTask[];
  installedTypes: JobType[];
  needsSeeding: boolean;
  currentPage: number;
  totalPages: number;
};

// --- REMIX LOADER ---
// Fetches available tasks and checks which ones are already installed.
export const loader = async ({ request }: LoaderFunctionArgs): Promise<Response> => {
    const { session } = await authenticate.admin(request);
    const url = new URL(request.url);

    const query = url.searchParams.get("query")?.toLowerCase() || "";
    const category = url.searchParams.get("category") || "all";
    const page = parseInt(url.searchParams.get("page") || "1", 10);

    // Check if seeding is needed: Compare the number of templates in TASKS_LIBRARY to the count in the DB
    const taskTemplateCount = await prisma.taskTemplate.count();
    const needsSeeding = TASKS_LIBRARY.length > taskTemplateCount;

    // Build the where clause dynamically
    const whereConditions: Prisma.TaskTemplateWhereInput[] = [];

    if (category !== 'all') {
      whereConditions.push({ category: category });
    }

    if (query) {
      whereConditions.push({
        OR: [
          // Use type assertion to allow 'mode' property
          { title: { contains: query, mode: 'insensitive' } as any },
          { description: { contains: query, mode: 'insensitive' } as any },
        ],
      });
    }
    const finalWhereClause = whereConditions.length > 0 ? { AND: whereConditions } : undefined;

    // 1. Fetch task templates from the database with pagination
    const [availableTasksFromDB, totalTasks] = await Promise.all([
      prisma.taskTemplate.findMany({
        where: finalWhereClause,
        orderBy: { id: 'asc' }, // Or any other preferred order
        skip: (page - 1) * PAGE_SIZE,
        take: PAGE_SIZE,
      }),
      prisma.taskTemplate.count({
        where: finalWhereClause,
      })
    ]);

    const totalPages = Math.ceil(totalTasks / PAGE_SIZE);

    // This mapping is just to ensure the shape matches what the component expects.
    // The `id` here is the manually assigned one from TASKS_LIBRARY, now stored in TaskTemplate.
    const availableTasks: DisplayTask[] = availableTasksFromDB.map(dbTask => ({
        id: dbTask.id,
        title: dbTask.title,
        description: dbTask.description,
        category: dbTask.category,
        type: dbTask.type, // This is JobType from Prisma
        trigger: dbTask.trigger,
    }));

    // 2. Get the types of automations the user has already installed
    const installedAutomations = await prisma.automation.findMany({
        where: { shop: session.shop },
        select: { type: true },
    });
    // installedAutomations.map(a => a.type) will produce JobType[]
    // Using Set and Array.from ensures uniqueness
    const installedTypes = Array.from(new Set(installedAutomations.map(a => a.type)));

    const data: LoaderData = { tasks: availableTasks, installedTypes, needsSeeding, currentPage: page, totalPages };
    return Response.json(data);
};

export const action = async ({ request }: ActionFunctionArgs) => {
    const formData = await request.formData();
    const { _action } = Object.fromEntries(formData);

    if (_action === "seedTaskTemplates") {
        for (const task of TASKS_LIBRARY) {
            // Destructure to separate model fields from extra fields like optionsSchema
            const { optionsSchema, ...taskTemplateData } = task;

            await prisma.taskTemplate.upsert({
                // Use the 'type' field as the unique identifier for the lookup.
                // This requires a @unique constraint on the 'type' field in your schema.
                where: { type: task.type }, 
                
                // If a record with this type is found, update it.
                update: {
                    ...taskTemplateData,
                },
                // If no record is found, create a new one.
                create: {
                    ...taskTemplateData,
                },
            });
        }
        return Response.json({ ok: true, message: "Task templates seeded." });
    }
    return Response.json({ ok: false, error: "Invalid action" }, { status: 400 });
};

export default function TaskLibrary() {
  const { tasks, installedTypes, needsSeeding, currentPage, totalPages } = useLoaderData<LoaderData>();
  console.log(needsSeeding);
  const [searchParams] = useSearchParams();
  const submit = useSubmit();
  const fetcher = useFetcher();

  useEffect(() => {
    if (needsSeeding && fetcher.state === 'idle') {
      console.log("Task templates need seeding. Initiating seed...");
      fetcher.submit({ _action: "seedTaskTemplates" }, { method: "post" });
    }
  }, [needsSeeding, fetcher, tasks.length]);

  const queryValue = searchParams.get("query") || "";
  const selectedCategory = searchParams.get("category") || "all";

  const selectedTabIndex = useMemo(() => {
    const index = TABS.findIndex(tab => tab.id === selectedCategory);
    return index === -1 ? 0 : index;
  }, [selectedCategory]);

  const handleTabChange = (index: number) => {
    const newCategory = TABS[index].id;
    const formData = new FormData();
    formData.set("query", queryValue);
    formData.set("category", newCategory);
    formData.set("page", "1"); // Reset to page 1 on category change
    submit(formData, { method: 'get', replace: true });
  }

  const handlePageChange = (newPage: number) => {
    const formData = new FormData();
    formData.set("query", queryValue);
    formData.set("category", selectedCategory);
    formData.set("page", newPage.toString());
    submit(formData, { method: 'get', replace: true });
  }

  const installedSet = new Set(installedTypes);

  // Display a message while seeding or if seeding failed and tasks are empty
  if (fetcher.state !== 'idle' && tasks.length === 0) {
    return (
        <Page title="Task Library">
            <Layout>
                <Layout.Section>
                    <Card>
                        <Text variant="bodyMd" as="p">Seeding task templates... Please wait.</Text>
                    </Card>
                </Layout.Section>
            </Layout>
        </Page>
    );
  }

  return (
    <Page title="Task Library">
      <Layout>
        <Layout.Section>
          <Form method="get">
            <input type="hidden" name="category" value={selectedCategory} />
            <TextField
              label="Search Tasks"
              labelHidden
              name="query"
              value={queryValue}
              onChange={(value) => {
                // submit both query + category back to the URL
                const formData = new FormData();
                formData.set("query", value);
                formData.set("category", selectedCategory);
                formData.set("page", "1"); // Reset to page 1 on search change
                submit(formData, { method: "get", replace: true });
              }}
              placeholder="Search for automation tasks..."
              autoComplete="off"
            />
          </Form>
        </Layout.Section>
        <Layout.Section>
          <Tabs
            tabs={TABS}
            selected={selectedTabIndex}
            onSelect={handleTabChange}
          />
        </Layout.Section>
        <Layout.Section>
          <Grid>
            {tasks.map(task => {
              const isInstalled = installedSet.has(task.type);
              return (
              <Grid.Cell key={task.id} columnSpan={{xs: 6, sm: 3, md: 3, lg: 4, xl: 4}}>
                <Card>
                    <BlockStack gap="300">
                        <Text variant="headingMd" as="h2">{task.title}</Text>
                        <Text variant="bodyMd" as="p" tone="subdued">{task.description}</Text>
                        <RemixLink 
                          to={isInstalled ? "/app/automations" : `/app/automations/configure/${task.id}`} 
                          style={{ textDecoration: 'none', display: 'block' }} // Ensures link styling doesn't interfere and button takes full width
                        >
                            <Button
                                variant={isInstalled ? "secondary" : "primary"}
                                fullWidth
                            >
                                {isInstalled ? "Manage" : "Configure & Add"}
                            </Button>
                        </RemixLink>
                    </BlockStack>
                </Card>
              </Grid.Cell>
            )})}
          </Grid>
          {tasks.length === 0 && !needsSeeding && fetcher.state === 'idle' && (
            <Card>
                <Text variant="bodyMd" as="p">
                    No tasks found matching your criteria.
                </Text>
            </Card>
          )}
        </Layout.Section>
        {tasks.length > 0 && totalPages > 1 && (
          <Layout.Section>
            <InlineStack align="center">
              <NumberedPagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </InlineStack>
          </Layout.Section>
        )}
      </Layout>
      {/* Add some spacing at the bottom of the page */}
      <div style={{ paddingBottom: 'var(--p-space-800)' }} />
    </Page>
  );
}