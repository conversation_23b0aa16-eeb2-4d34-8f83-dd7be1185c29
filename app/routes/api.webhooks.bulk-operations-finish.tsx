import type { ActionFunctionArgs } from "@remix-run/node";
import { JobStatus } from "@prisma/client";
import { authenticate } from "../shopify.server";
import { updateJobStatus } from "../queues/jobQueue.server";
import { logToFile } from "../utils/logger.server";
import prisma from "../db.server";
import fetch from "node-fetch";
import { bulkOperationHandlers } from "../bulkOperationHandlers";
import type { BulkOperationJsonL } from "../bulkOperationHandlers";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { topic, shop, admin, payload } = await authenticate.webhook(request);

  if (!admin) {
    throw new Response("Unauthorized", { status: 401 });
  }

  let trackingJob; // Accessible in the catch block

  if (topic === "BULK_OPERATIONS_FINISH") {
    const logContext = `bulk-operations-finish-${shop}`;
    logToFile(logContext, "info", `Received ${topic} webhook.`);
    logToFile(logContext, "info", `bulk operation payload: ${JSON.stringify(payload)}`);

    try {
      const bulkOperationGid = payload.admin_graphql_api_id;
      if (!bulkOperationGid) {
        logToFile(logContext, "error", "Missing admin_graphql_api_id in payload.");
        return new Response("Missing required fields", { status: 400 });
      }

      trackingJob = await prisma.job.findFirst({
        where: {
          shop,
          data: { contains: `"${bulkOperationGid}"` },
          status: { in: [JobStatus.processing, JobStatus.pending] },
        },
      });

      if (!trackingJob) {
        logToFile(logContext, "warn", `No matching job found for GID: ${bulkOperationGid}. Might be a duplicate or old webhook.`);
        return new Response("No matching job found", { status: 200 });
      }

      if (payload.status.toUpperCase() !== JobStatus.completed.toUpperCase()) {
        const errorMessage = `Bulk operation failed. Status: ${payload.status}, Error: ${payload.error_code}`;
        logToFile(logContext, "warn", errorMessage);
        await updateJobStatus(trackingJob.id, JobStatus.failed, errorMessage);
        return new Response("Bulk operation did not complete", { status: 200 });
      }

      // --- Download and Parse ---
      const getUrlQuery = `#graphql
        query GetBulkOperationUrl($id: ID!) {
          node(id: $id) { ... on BulkOperation { url } }
        }`;
      const urlResponse = await admin.graphql(getUrlQuery, { variables: { id: bulkOperationGid } });
      const urlData = await urlResponse.json();
      const url = urlData.data?.node?.url;

      if (!url) {
        logToFile(logContext, "info", `Operation ${bulkOperationGid} completed with no result file.`);
        await updateJobStatus(trackingJob.id, JobStatus.completed, "Completed with no result file.");
        return new Response("No result file", { status: 200 });
      }

      const response = await fetch(url);
      if (!response.ok) throw new Error(`Failed to download results: ${response.statusText}`);
      
      const text = await response.text();
      const lines: BulkOperationJsonL[] = text.split('\n').filter(Boolean).map(function(item: string) { return JSON.parse(item); });

      // --- Dispatch to Handler ---
      const automation = await prisma.automation.findUnique({ where: { id: JSON.parse(trackingJob.data as string).automationId } });
      if (!automation) throw new Error(`Automation not found for job ${trackingJob.id}`);

      const handler = bulkOperationHandlers[trackingJob.type];
      if (!handler) throw new Error(`No handler for job type: ${trackingJob.type}`);

      logToFile(logContext, "info", `Dispatching to handler for job type: ${trackingJob.type}. Processing ${lines.length} lines.`);
      const { processedCount } = await handler(shop, lines, automation.config ?? {}, admin);

      // --- Finalize Job ---
      const finalMessage = `Successfully processed ${processedCount} items.`;
      await updateJobStatus(trackingJob.id, JobStatus.completed, finalMessage);
      logToFile(logContext, "info", finalMessage);

      return new Response("Webhook processed successfully", { status: 200 });

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logToFile(logContext, "error", `Processing failed: ${errorMessage}`);
      if (trackingJob) {
        await updateJobStatus(trackingJob.id, JobStatus.failed, `Processing failed: ${errorMessage}`);
      }
      return new Response("Failed to process webhook", { status: 500 });
    }
  }

  // Fallback for other topics
  throw new Response("Unhandled webhook topic", { status: 404 });
};
