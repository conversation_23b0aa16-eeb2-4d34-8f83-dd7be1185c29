import { type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useMemo, useState, useEffect } from "react";
import { useFetcher, useLoaderData } from "@remix-run/react";
import {
  Badge,
  BlockStack,
  Card,
  InlineStack,
  Layout,
  Page,
  ResourceItem,
  ResourceList,
  Spinner,
  Text,
  Toast,
  type ResourceListProps,
} from "@shopify/polaris";
import { JobType, type Automation } from "@prisma/client";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { usePollingWithFeedback } from "app/hooks/usePollingWithFeedback";
import { getAutomationsWithStatus } from "app/services/automations.server";
import { startBulkOperation } from "app/services/bulkOperations.server";
import { addJob } from "app/queues/jobQueue.server";

// This is the type of data we get from the loader after JSON serialization
type SerializedAutomationWithStatus = Omit<Automation, 'createdAt' | 'updatedAt' | 'lastRunAt'> & {
  running: boolean;
  createdAt: string;
  updatedAt: string;
  lastRunAt: string | null;
};

// This is the type we use in the component after rehydrating the dates
type AutomationWithStatus = Automation & { running: boolean };

// LOADER
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const automationsWithStatus = await getAutomationsWithStatus(session.shop);
  return Response.json({ automations: automationsWithStatus });
};

// ACTION
export const action = async ({ request }: ActionFunctionArgs) => {
  const { session, admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const { _action, id, status } = Object.fromEntries(formData);

  const automationId = parseInt(id as string, 10);
  if (isNaN(automationId)) {
    return Response.json({ error: "Invalid automation ID" }, { status: 400 });
  }

  const automation = await prisma.automation.findUnique({ where: { id: automationId, shop: session.shop } });
  if (!automation) {
    return Response.json({ error: "Automation not found" }, { status: 404 });
  }

  switch (_action) {
    case "toggleStatus":
      await prisma.automation.update({
        where: { id: automationId, shop: session.shop },
        data: { status: status as string },
      });
      return Response.json({ ok: true });

    

    case "runBulk":
      // Special handling for Collection Visibility. Instead of a Shopify Bulk Operation,
      // we enqueue a custom, collection-centric job.
      if (automation.type === JobType.COLLECTION_VISIBILITY_UPDATE) {
        await addJob({
          shop: session.shop,
          jobType: JobType.COLLECTION_VISIBILITY_BULK_UPDATE,
          data: { automationId: automation.id }, // Pass automationId for context if needed
        });
        await prisma.automation.update({
          where: { id: automationId },
          data: { lastRunAt: new Date() },
        });
        return Response.json({ ok: true, message: "Bulk collection visibility check has been scheduled." });
      }

      // Default handling for other bulk operations that use Shopify's bulk query runner.
      const result = await startBulkOperation(admin, automation);
      if (result.success) {
        await prisma.automation.update({
          where: { id: automationId },
          data: { lastRunAt: new Date() },
        });
        return Response.json({ ok: true, message: result.message });
      } else {
        return Response.json({ error: result.error }, { status: 500 });
      }

    default:
      return Response.json({ error: "Invalid action" }, { status: 400 });
  }
}

export default function Automations() {
  // 1. Get the serialized data from the loader
  const loaderData = useLoaderData<{ automations: SerializedAutomationWithStatus[] }>();

  // 2. Rehydrate the data, converting date strings back to Date objects
  const automations: AutomationWithStatus[] = useMemo(() => {
    return loaderData.automations.map(auto => ({
      ...auto,
      createdAt: new Date(auto.createdAt),
      updatedAt: new Date(auto.updatedAt),
      lastRunAt: auto.lastRunAt ? new Date(auto.lastRunAt) : null,
    }));
  }, [loaderData.automations]);

  const fetcher = useFetcher<typeof action>();
  
  usePollingWithFeedback(30000);

  // State for toast notifications
  const [toastContent, setToastContent] = useState("");
  const [isToastError, setIsToastError] = useState(false);
  const [toastActive, setToastActive] = useState(false);

  // State to track the specific automation being run
  const [runningAutomationId, setRunningAutomationId] = useState<number | null>(null);

  // Effect to handle feedback from the fetcher
  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data) {
      if ('error' in fetcher.data && fetcher.data.error) {
        setToastContent(fetcher.data.error);
        setIsToastError(true);
        setToastActive(true);
      } else if ('message' in fetcher.data && fetcher.data.message) {
        setToastContent(fetcher.data.message);
        setIsToastError(false);
        setToastActive(true);
      }
      // Reset the running ID after the action is complete
      setRunningAutomationId(null);
    }
  }, [fetcher.data, fetcher.state]);

  const handleToggle = (id: number, currentStatus: string) => {
    fetcher.submit(
      { _action: "toggleStatus", id: id.toString(), status: currentStatus === "Active" ? "Inactive" : "Active" },
      { method: "post" }
    );
  };

  const handleBulkRun = (id: number) => {
    setRunningAutomationId(id); // Set the ID of the automation being run
    fetcher.submit({ _action: "runBulk", id: id.toString() }, { method: "post" });
  };

  

  // 3. Define the renderItem function with the correct, rehydrated type
  const renderItem: ResourceListProps<AutomationWithStatus>['renderItem'] = (item) => {
    const { id, name, trigger, lastRunAt, running, status, type } = item;
    const isActive = status === "Active";

    const isStarting = fetcher.state !== 'idle' && runningAutomationId === id;

    const shortcutActions = [
      { content: isActive ? "Disable" : "Enable", onAction: () => handleToggle(id, status), disabled: running || fetcher.state !== 'idle' },
    ];

    const bulkRunnableTypes = [
      JobType.AUTO_TAG_ORDERS_UTM,
      JobType.ORDER_COLLECTION_TAG,
      JobType.COLLECTION_VISIBILITY_UPDATE,
      JobType.AUTO_TAG_CUSTOMER_BY_VENDOR,
    ] as const;

    if (isActive && bulkRunnableTypes.includes(type as any)) {
      shortcutActions.unshift({
        content: isStarting ? "Starting..." : "Run Now",
        onAction: () => handleBulkRun(id),
        disabled: running || fetcher.state !== 'idle',
      });
    }

    return (
      <ResourceItem
        id={id.toString()}
        url={`/app/automations/configure/${id}`}
        accessibilityLabel={`Configure ${name}`}
        shortcutActions={shortcutActions}
        persistActions
      >
        <BlockStack gap="100">
          <InlineStack gap="200" align="start" blockAlign="center">
            <Text variant="bodyMd" fontWeight="bold" as="h3">{name}</Text>
            {running && <Spinner accessibilityLabel="Automation is currently running" size="small" />}
            <Badge tone={isActive ? "success" : "attention"}>{status}</Badge>
          </InlineStack>
          <Text as="p" variant="bodySm" tone="subdued">Trigger: {trigger}</Text>
          <Text as="p" variant="bodySm" tone="subdued">Last Run: {lastRunAt ? lastRunAt.toLocaleString() : 'Never'}</Text>
          {item.config && typeof item.config === 'object' && 'delay' in item.config && item.config.delay && typeof item.config.delay === 'object' && 'type' in item.config.delay && item.config.delay.type !== 'immediate' && (
            <Text as="p" variant="bodySm" tone="subdued">
              Scheduled Delay: {item.config.delay.type === 'relative' && 'value' in item.config.delay && 'unit' in item.config.delay ? `${item.config.delay.value} ${item.config.delay.unit}` : 'value' in item.config.delay && 'timestamp' in item.config.delay ? `Specific time: ${item.config.delay.timestamp}` : 'Configured delay'}
            </Text>
          )}
        </BlockStack>
      </ResourceItem>
    );
  };

  const toastMarkup = toastActive ? (
    <Toast content={toastContent} onDismiss={() => setToastActive(false)} error={isToastError} />
  ) : null;

  return (
    <Page
      title="My Automations"
      primaryAction={{ content: "Browse Task Library", url: "/app/library" }}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <ResourceList<AutomationWithStatus>
              resourceName={{ singular: "automation", plural: "automations" }}
              items={automations}
              renderItem={renderItem}
            />
          </Card>
        </Layout.Section>
        
      </Layout>
      {toastMarkup}
      <div style={{ paddingBottom: 'var(--p-space-800)' }} />
    </Page>
  );
}