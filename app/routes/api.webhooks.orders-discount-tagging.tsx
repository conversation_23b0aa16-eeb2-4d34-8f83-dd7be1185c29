import type { ActionFunctionArgs } from "@remix-run/node";
import { JobType } from "@prisma/client";
import { authenticate } from "../shopify.server";
import { createAutomationJob } from "../services/automations.server";
import { logToFile } from "../utils/logger.server";
import prisma from "../db.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { topic, shop, admin, payload } = await authenticate.webhook(request);

  if (!admin) {
    throw new Response("Unauthorized", { status: 401 });
  }

  switch (topic) {
    case "ORDERS_CREATE":
      logToFile("webhook-orders-discount-tagging", "info", `Received ${topic} webhook for shop: ${shop}`);
      logToFile("webhook-orders-discount-tagging", "info", `Payload: ${JSON.stringify(payload)}`);
      try {
        const orderId = payload.admin_graphql_api_id;
        const discountCodes = payload.discount_codes?.map((code: { code: string }) => code.code) || [];

        if (!orderId) {
          logToFile("webhook-orders-discount-tagging", "error", `Missing order ID in webhook payload for shop: ${shop}`);
          return Response.json({ message: "Missing order ID" }, { status: 400 });
        }

        const activeAutomation = await prisma.automation.findFirst({
          where: {
            shop,
            type: JobType.ORDER_DISCOUNT_TAG,
            status: "Active",
          },
        });

        if (!activeAutomation) {
          logToFile("webhook-orders-discount-tagging", "info", `No active ORDER_DISCOUNT_TAG automation found for shop ${shop}`);
          return Response.json({ message: "Automation not active" }, { status: 200 });
        }

        if (discountCodes.length === 0) {
          logToFile("webhook-orders-discount-tagging", "info", `No discount codes found for order ${orderId}. Skipping job creation.`);
          return Response.json({ message: "No discount codes to tag" }, { status: 200 });
        }

        // --- DE-DUPLICATION LOGIC ---
        const existingJob = await prisma.job.findFirst({
          where: {
            shop,
            type: JobType.ORDER_DISCOUNT_TAG,
            data: {
              contains: `"orderId":"${orderId}"`, // Specific check for orderId
            },
          },
        });

        if (existingJob) {
          logToFile("webhook-orders-discount-tagging", "info", `Skipping duplicate job enqueue for order ID: ${orderId}. A pending job already exists.`);
          return Response.json({ message: "Duplicate job request" }, { status: 200 });
        }
        // --- END DE-DUPLICATION LOGIC ---

        const jobData = {
          orderId,
          discountCodes,
        };

        await createAutomationJob(shop, JobType.ORDER_DISCOUNT_TAG, jobData);

        logToFile("webhook-orders-discount-tagging", "info", `Successfully enqueued ORDER_DISCOUNT_TAG job for order ID: ${orderId} for shop: ${shop}`);
        return Response.json({ message: "Job enqueued successfully" }, { status: 200 });
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logToFile("webhook-orders-discount-tagging", "error", `Failed to enqueue job for ${topic} webhook for shop: ${shop}: ${errorMessage}`);
        return Response.json({ message: "Failed to enqueue job" }, { status: 500 });
      }
    default:
      logToFile("webhook-orders-discount-tagging", "warn", `Unhandled webhook topic: ${topic} for shop: ${shop}`);
      throw new Response("Unhandled webhook topic", { status: 404 });
  }
};