import { type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useFetcher, useLoaderData, useSubmit, useNavigation, Form } from "@remix-run/react";
import { useState, useEffect } from "react";
import {
  Badge,
  BlockStack,
  Card,
  InlineStack,
  Layout,
  Page,
  ResourceItem,
  ResourceList,
  Text,
  Toast,
  TextField,
  Select,
  EmptyState,
  type ResourceListProps,
} from "@shopify/polaris";
import { JobStatus, JobType, type Job, type Prisma } from "@prisma/client";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { addJob } from "../queues/jobQueue.server";
import { NumberedPagination } from "../components/NumberedPagination";
import { formatJobName, formatRelativeTime, getStatusBadgeTone } from "../utils/formatting";
import { usePollingWithFeedback } from "app/hooks/usePollingWithFeedback";

const PAGE_SIZE = 10; // Number of jobs per page

type LoaderData = {
  jobs: Job[];
  currentPage: number;
  totalPages: number;
  searchQuery: string;
  statusFilter: string;
  availableStatuses: JobStatus[];
};

type JobWithDates = Omit<Job, 'createdAt' | 'updatedAt' | 'completedAt' | 'startedAt' | 'scheduledAt' | 'lockedAt'> & {
  createdAt: Date;
  updatedAt: Date;
  completedAt: Date | null;
  startedAt: Date | null;
  scheduledAt: Date | null;
  lockedAt: Date | null;
};

// LOADER
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const url = new URL(request.url);
  const searchQuery = url.searchParams.get("query")?.trim() || "";
  const statusFilter = url.searchParams.get("status") || "all";
  const page = parseInt(url.searchParams.get("page") || "1", 10);

  type JobWhereInput = Prisma.JobWhereInput;
  const whereConditions: JobWhereInput[] = [{ shop: session.shop }];

  if (searchQuery) {
    const matchingJobTypes = Object.values(JobType).filter(jt =>
      formatJobName(jt).toLowerCase().includes(searchQuery.toLowerCase())
    );
    whereConditions.push({ type: { in: matchingJobTypes } });
  }

  if (statusFilter !== "all") {
    if (Object.values(JobStatus).includes(statusFilter as JobStatus)) {
      whereConditions.push({ status: statusFilter as JobStatus });
    }
  }

  const whereClause = { AND: whereConditions.length > 1 ? whereConditions : whereConditions[0] };

  const [jobs, totalJobs] = await prisma.$transaction([
    prisma.job.findMany({
      where: whereClause,
      orderBy: { createdAt: "desc" },
      skip: (page - 1) * PAGE_SIZE,
      take: PAGE_SIZE,
    }),
    prisma.job.count({ where: whereClause }),
  ]);

  const totalPages = Math.ceil(totalJobs / PAGE_SIZE);

  return Response.json({
    jobs,
    currentPage: page,
    totalPages,
    searchQuery,
    statusFilter,
    availableStatuses: Object.values(JobStatus),
  });
};

// ACTION
export const action = async ({ request }: ActionFunctionArgs) => {
  await authenticate.admin(request);
  const formData = await request.formData();
  const { _action, id } = Object.fromEntries(formData);

  const jobId = id as string;

  switch (_action) {
    case "retryJob":
      const jobToRetry = await prisma.job.findUnique({ where: { id: jobId } });
      if (jobToRetry) {
        await addJob({
          shop: jobToRetry.shop,
          jobType: jobToRetry.type,
          data: JSON.parse(jobToRetry.data),
        });
        return Response.json({ ok: true, message: "Job has been re-queued successfully." });
      }
      return Response.json({ error: "Job not found." }, { status: 404 });

    case "cancelJob":
      await prisma.job.update({
        where: { id: jobId },
        data: { status: "canceled", errorMessage: "Job canceled by user." },
      });
      return Response.json({ ok: true, message: "Job has been canceled successfully." });

    default:
      return Response.json({ error: "Invalid action" }, { status: 400 });
  }
};

export default function Jobs() {
  const { jobs: serializedJobs, currentPage, totalPages, searchQuery, statusFilter, availableStatuses } = useLoaderData<LoaderData>();
  const fetcher = useFetcher<typeof action>();
  const submit = useSubmit();
  const navigation = useNavigation();

  const [queryValue, setQueryValue] = useState(searchQuery);
  const [currentStatusFilter, setCurrentStatusFilter] = useState(statusFilter);

  const [toastContent, setToastContent] = useState("");
  const [isToastError, setIsToastError] = useState(false);
  const [toastActive, setToastActive] = useState(false);

  const { formattedTime, isFlashing } = usePollingWithFeedback(30000);

  const handleToastDismiss = () => setToastActive(false);

  useEffect(() => {
    setQueryValue(searchQuery);
  }, [searchQuery]);

  useEffect(() => {
    setCurrentStatusFilter(statusFilter);
  }, [statusFilter]);

  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data) {
      if ('error' in fetcher.data && fetcher.data.error) {
        setToastContent(fetcher.data.error);
        setIsToastError(true);
        setToastActive(true);
      } else if ('message' in fetcher.data && fetcher.data.message) {
        setToastContent(fetcher.data.message);
        setIsToastError(false);
        setToastActive(true);
      }
    }
  }, [fetcher.data, fetcher.state]);

  const jobs: JobWithDates[] = serializedJobs.map(job => ({
    ...job,
    createdAt: new Date(job.createdAt),
    updatedAt: new Date(job.updatedAt),
    completedAt: job.completedAt ? new Date(job.completedAt) : null,
    startedAt: job.startedAt ? new Date(job.startedAt) : null,
    scheduledAt: job.scheduledAt ? new Date(job.scheduledAt) : null,
    lockedAt: job.lockedAt ? new Date(job.lockedAt) : null,
  }));

  const handlePageChange = (page: number) => {
    const formData = new FormData();
    formData.set("query", queryValue);
    formData.set("status", currentStatusFilter);
    formData.set("page", page.toString());
    submit(formData, { method: "get", replace: true });
  };

  const handleQueryChange = (value: string) => {
    setQueryValue(value);
    const formData = new FormData();
    formData.set("query", value);
    formData.set("status", currentStatusFilter);
    formData.set("page", "1"); // Reset to page 1 on new search/filter
    submit(formData, { method: "get", replace: true });
  };

  const handleStatusChange = (value: string) => {
    setCurrentStatusFilter(value);
    const formData = new FormData();
    formData.set("query", queryValue);
    formData.set("status", value);
    formData.set("page", "1"); // Reset to page 1 on new search/filter
    submit(formData, { method: "get", replace: true });
  };

  const renderItem: ResourceListProps<JobWithDates>['renderItem'] = (item) => {
    const { id, type, status, createdAt, startedAt, completedAt, errorMessage } = item;

    const isSubmitting = fetcher.state !== 'idle' && fetcher.formData?.get('id') === id;

    const shortcutActions = [];
    if (status === "failed") {
      shortcutActions.push({
        content: "Retry",
        onAction: () => fetcher.submit({ _action: "retryJob", id }, { method: "post" }),
        loading: isSubmitting,
        disabled: isSubmitting,
      });
    }
    if (status === "processing") {
      shortcutActions.push({
        content: "Cancel",
        onAction: () => fetcher.submit({ _action: "cancelJob", id }, { method: "post" }),
        destructive: true,
        loading: isSubmitting,
        disabled: isSubmitting,
      });
    }

    return (
      <ResourceItem
        id={id}
        url={`/app/jobs/${id}`}
        accessibilityLabel={`View details for job ${id}`}
        shortcutActions={shortcutActions}
        persistActions
      >
        <BlockStack gap="100">
          <InlineStack gap="200" align="start" blockAlign="center">
            <Text variant="bodyMd" fontWeight="bold" as="h3">{formatJobName(type)}</Text>
            <Badge tone={getStatusBadgeTone(status)}>{status}</Badge>
          </InlineStack>
          <Text as="p" variant="bodySm" tone="subdued">Created: {formatRelativeTime(createdAt)}</Text>
          {startedAt && <Text as="p" variant="bodySm" tone="subdued">Started: {startedAt.toLocaleString()}</Text>}
          {completedAt && <Text as="p" variant="bodySm" tone="subdued">Completed: {completedAt.toLocaleString()}</Text>}
          {errorMessage && <Text as="p" variant="bodySm" tone="critical">Error: {errorMessage}</Text>}
        </BlockStack>
      </ResourceItem>
    );
  };

  const statusOptions = [
    { label: "All Statuses", value: "all" },
    ...availableStatuses.map(status => ({
        label: status.charAt(0).toUpperCase() + status.slice(1), // Capitalize
        value: status
    }))
  ];

  const isLoading = navigation.state === "loading" || navigation.state === "submitting";

  return (
    <Page title="Job Status">
      <Layout>
        <Layout.Section>
          <Form method="get">
            <InlineStack gap="400" wrap={true} blockAlign="end">
              <div style={{ flex: "1 1 300px" }}>
                <TextField
                  label="Search Jobs"
                  name="query"
                  value={queryValue}
                  onChange={handleQueryChange}
                  placeholder="Search by job type..."
                  autoComplete="off"
                  labelHidden
                />
              </div>
              <div style={{ flex: "0 1 200px" }}>
                <Select
                  label="Filter by Status"
                  labelHidden
                  name="status"
                  options={statusOptions}
                  value={currentStatusFilter}
                  onChange={handleStatusChange}
                />
              </div>
              <Text as="p" variant="bodySm" tone="subdued" alignment="end">
                <Text as="span" tone={isFlashing ? "success" : "subdued"}>{formattedTime}</Text>
              </Text>
            </InlineStack>
          </Form>
        </Layout.Section>
        <Layout.Section>
          <Card padding="0">
            {jobs.length > 0 ? (
              <ResourceList<JobWithDates>
                resourceName={{ singular: "job", plural: "jobs" }}
                items={jobs}
                renderItem={renderItem}
                loading={isLoading}
              />
            ) : (
              <EmptyState
                heading="No jobs found"
                action={{
                  content: "Clear Filters",
                  onAction: () => {
                    setQueryValue("");
                    setCurrentStatusFilter("all");
                    submit({ query: "", status: "all", page: "1" }, { method: "get", replace: true });
                  },
                }}
                image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
              >
                <p>No jobs match your current filters. Try adjusting your search or status filter.</p>
              </EmptyState>
            )}
            {totalPages > 1 && (
              <div style={{ marginTop: "var(--p-space-400)", padding: "var(--p-space-400)" }}>
                <NumberedPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </Card>
        </Layout.Section>
      </Layout>
      {toastActive && (
        <Toast content={toastContent} error={isToastError} onDismiss={handleToastDismiss} />
      )}
      <div style={{ paddingBottom: 'var(--p-space-800)' }} />
    </Page>
  );
}
