import type { ActionFunctionArgs } from "@remix-run/node";
import { JobType } from "@prisma/client";
import { authenticate } from "../shopify.server";
import { createAutomationJob } from "../services/automations.server";
import { logToFile } from "../utils/logger.server";
import prisma from "../db.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { topic, shop, admin, payload } = await authenticate.webhook(request);

  if (!admin) {
    // If we can't get an admin client, we can't do anything.
    // This might happen if the app was uninstalled but a webhook was still in flight.
    throw new Response("Unauthorized", { status: 401 });
  }

  switch (topic) {
    case "ORDERS_CREATE":
      logToFile("webhook-orders-collection-tagging", "info", `Received ${topic} webhook for shop: ${shop}`);
      try {
        // Find if there is an active automation for this type and shop.
        const activeAutomation = await prisma.automation.findFirst({
          where: {
            shop,
            type: JobType.ORDER_COLLECTION_TAG,
            status: "Active",
          },
        });

        if (!activeAutomation) {
          logToFile("webhook-orders-collection-tagging", "info", `No active ORDER_COLLECTION_TAG automation found for shop ${shop}. Skipping job creation.`);
          // Return a 200 OK because there's no error; the user simply doesn't have this automation active.
          return new Response("Automation not active", { status: 200 });
        }

        const orderId = payload.id;
        if (!orderId) {
          logToFile("webhook-orders-collection-tagging", "error", `Missing order ID in webhook payload for shop: ${shop}`);
          return new Response("Missing order ID", { status: 400 });
        }
        // Construct the full GraphQL GID for the order.
        const orderGid = `gid://shopify/Order/${orderId}`;

        // Prepare the data payload for the job.
        // The job handler will need the order GID and the specific configuration for this automation.
        const jobData = {
          orderId: orderGid,
          config: activeAutomation.config ?? {}, // Use the saved config, defaulting to an empty object.
        };

        // Enqueue the job for background processing with potential delay.
        await createAutomationJob(shop, JobType.ORDER_COLLECTION_TAG, jobData);

        logToFile("webhook-orders-collection-tagging", "info", `Successfully enqueued ORDER_COLLECTION_TAG job for order ID: ${orderId} for shop: ${shop}`);
        // Respond to Shopify that the webhook was received and handled successfully.
        return new Response("Job enqueued successfully", { status: 200 });

      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logToFile("webhook-orders-collection-tagging", "error", `Failed to enqueue job for ${topic} webhook for shop: ${shop}: ${errorMessage}`);
        // If something goes wrong, return a 500 error so Shopify will retry the webhook.
        return new Response("Failed to enqueue job", { status: 500 });
      }

    default:
      // This handler is only for ORDERS_CREATE. If it receives anything else, ignore it.
      logToFile("webhook-orders-collection-tagging", "warn", `Unhandled webhook topic: ${topic} for shop: ${shop}`);
      throw new Response("Unhandled webhook topic", { status: 404 });
  }
};
