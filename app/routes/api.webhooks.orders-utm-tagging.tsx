import type { ActionFunctionArgs } from "@remix-run/node";
import { JobType } from "@prisma/client";
import { authenticate } from "../shopify.server";
import { createAutomationJob } from "../services/automations.server";
import { logToFile } from "../utils/logger.server";
import prisma from "../db.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { topic, shop, admin, payload } = await authenticate.webhook(request);

  if (!admin) {
    throw new Response("Unauthorized", { status: 401 });
  }

  switch (topic) {
    case "ORDERS_CREATE":
      logToFile("webhook-orders-utm-tagging", "info", `Received ${topic} webhook for shop: ${shop}`);
      logToFile("webhook-orders-utm-tagging", "info", `Payload: ${JSON.stringify(payload)}`);
      try {
        const orderId = payload.id;
        if (!orderId) {
          logToFile("webhook-orders-utm-tagging", "error", `Missing order ID in webhook payload for shop: ${shop}`);
          return Response.json({ message: "Missing order ID" }, { status: 400 });
        }
        const orderGid = `gid://shopify/Order/${orderId}`;

        const activeAutomation = await prisma.automation.findFirst({
          where: {
            shop,
            type: JobType.AUTO_TAG_ORDERS_UTM,
            status: "Active",
          },
        });

        if (!activeAutomation) {
          logToFile("webhook-orders-utm-tagging", "info", `No active AUTO_TAG_ORDERS_UTM automation found for shop ${shop}`);
          return Response.json({ message: "Automation not active" }, { status: 200 });
        }

        // --- DE-DUPLICATION LOGIC ---
        // Check if a pending job for this specific order already exists.
        const existingJob = await prisma.job.findFirst({
          where: {
            shop,
            type: JobType.AUTO_TAG_ORDERS_UTM,
            data: {
              contains: `"${orderGid}"`, // Using a slightly more specific contains to avoid accidental matches
            },
          },
        });

        if (existingJob) {
          logToFile("webhook-orders-utm-tagging", "info", `Skipping duplicate job enqueue for order ID: ${orderGid}. A pending job already exists.`);
          return Response.json({ message: "Duplicate job request" }, { status: 200 });
        }
        // --- END DE-DUPLICATION LOGIC ---

        const config = activeAutomation.config ?? {};
        const jobData = {
          orderId: orderGid,
          config,
        };

        await createAutomationJob(shop, JobType.AUTO_TAG_ORDERS_UTM, jobData);

        logToFile("webhook-orders-utm-tagging", "info", `Successfully enqueued AUTO_TAG_ORDERS_UTM job for order ID: ${orderId} for shop: ${shop}`);
        return Response.json({ message: "Job enqueued successfully" }, { status: 200 });
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logToFile("webhook-orders-utm-tagging", "error", `Failed to enqueue job for ${topic} webhook for shop: ${shop}: ${errorMessage}`);
        return Response.json({ message: "Failed to enqueue job" }, { status: 500 });
      }
    default:
      logToFile("webhook-orders-utm-tagging", "warn", `Unhandled webhook topic: ${topic} for shop: ${shop}`);
      throw new Response("Unhandled webhook topic", { status: 404 });
  }
};
