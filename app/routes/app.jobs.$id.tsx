import { type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useFetcher } from "@remix-run/react";
import { useState, useEffect } from "react";
import {
  Card,
  Layout,
  Page,
  Text,
  BlockStack,
  InlineStack,
  Badge,
  Button,
  Toast,
  Icon,
} from "@shopify/polaris";
import { ClipboardIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { formatJobName, formatRelativeTime, getStatusBadgeTone } from "../utils/formatting";
import { JobStatus } from "@prisma/client";
import { usePollingWithFeedback } from "app/hooks/usePollingWithFeedback";
import { addJob } from "../queues/jobQueue.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  await authenticate.admin(request);
  const formData = await request.formData();
  const { _action, id } = Object.fromEntries(formData);

  const jobId = id as string;

  switch (_action) {
    case "retryJob":
      const jobToRetry = await prisma.job.findUnique({ where: { id: jobId } });
      if (jobToRetry) {
        await addJob({
          shop: jobToRetry.shop,
          jobType: jobToRetry.type,
          data: JSON.parse(jobToRetry.data),
        });
        return Response.json({ ok: true, message: "Job has been re-queued successfully." });
      }
      return Response.json({ error: "Job not found." }, { status: 404 });

    case "cancelJob":
      await prisma.job.update({
        where: { id: jobId },
        data: { status: "canceled", errorMessage: "Job canceled by user." },
      });
      return Response.json({ ok: true, message: "Job has been canceled successfully." });

    default:
      return Response.json({ error: "Invalid action" }, { status: 400 });
  }
};

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  await authenticate.admin(request);
  const { id } = params;

  if (!id) {
    throw new Response("Job ID not provided", { status: 400 });
  }

  const job = await prisma.job.findUnique({
    where: { id },
  });

  if (!job) {
    throw new Response("Job not found", { status: 404 });
  }

  // Rehydrate dates for client-side use
  const serializedJob = {
    ...job,
    createdAt: job.createdAt.toISOString(),
    updatedAt: job.updatedAt.toISOString(),
    startedAt: job.startedAt?.toISOString() || null,
    completedAt: job.completedAt?.toISOString() || null,
    scheduledAt: job.scheduledAt?.toISOString() || null,
    lockedAt: job.lockedAt?.toISOString() || null,
  };

  return Response.json({ job: serializedJob });
};

export default function JobDetails() {
  const { job: serializedJob } = useLoaderData<typeof loader>();
  const fetcher = useFetcher<typeof action>();

  // Rehydrate dates from serialized strings
  const job = {
    ...serializedJob,
    createdAt: new Date(serializedJob.createdAt),
    updatedAt: new Date(serializedJob.updatedAt),
    startedAt: serializedJob.startedAt ? new Date(serializedJob.startedAt) : null,
    completedAt: serializedJob.completedAt ? new Date(serializedJob.completedAt) : null,
    scheduledAt: serializedJob.scheduledAt ? new Date(serializedJob.scheduledAt) : null,
    lockedAt: serializedJob.lockedAt ? new Date(serializedJob.lockedAt) : null,
  };

  const [toastContent, setToastContent] = useState("");
  const [isToastError, setIsToastError] = useState(false);
  const [toastActive, setToastActive] = useState(false);

  const handleToastDismiss = () => setToastActive(false);

  const { formattedTime, isFlashing } = usePollingWithFeedback(30000);

  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data) {
      if ('error' in fetcher.data && fetcher.data.error) {
        setToastContent(fetcher.data.error);
        setIsToastError(true);
        setToastActive(true);
      } else if ('message' in fetcher.data && fetcher.data.message) {
        setToastContent(fetcher.data.message);
        setIsToastError(false);
        setToastActive(true);
      }
    }
  }, [fetcher.data, fetcher.state]);

  const handleCopy = async (text: string, message: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setToastContent(message);
      setIsToastError(false);
      setToastActive(true);
    } catch (err) {
      setToastContent("Failed to copy.");
      setIsToastError(true);
      setToastActive(true);
    }
  };

  const isSubmitting = fetcher.state !== 'idle';

  const actionButtons: { content: string; onAction: () => void; loading?: boolean; disabled?: boolean; destructive?: boolean; }[] = [];
  if (job.status === JobStatus.failed) {
    actionButtons.push({
      content: "Retry Job",
      onAction: () => fetcher.submit({ _action: "retryJob", id: job.id }, { method: "post" }),
      loading: isSubmitting,
      disabled: isSubmitting,
    });
  }
  if (job.status === JobStatus.processing) {
    actionButtons.push({
      content: "Cancel Job",
      onAction: () => fetcher.submit({ _action: "cancelJob", id: job.id }, { method: "post" }),
      destructive: true,
      loading: isSubmitting,
      disabled: isSubmitting,
    });
  }

  return (
    <Page
      title={`Job: ${formatJobName(job.type)}`}
      backAction={{ content: "Jobs", url: "/app/jobs" }}
      primaryAction={actionButtons.length > 0 ? actionButtons[0] : undefined}
      secondaryActions={actionButtons.length > 1 ? [actionButtons[1]] : undefined}
    >
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <InlineStack gap="200" align="start" blockAlign="center">
                <Text variant="headingMd" as="h2">Job Details</Text>
                <Badge tone={getStatusBadgeTone(job.status)}>{job.status}</Badge>
              </InlineStack>
              <InlineStack gap="200" blockAlign="center">
                <Text as="p" variant="bodyMd"><strong>ID:</strong> {job.id}</Text>
                <Button
                  icon={<Icon source={ClipboardIcon} accessibilityLabel="Copy ID" />}
                  onClick={() => handleCopy(job.id, "Job ID copied to clipboard!")}
                />
              </InlineStack>
              <Text as="p" variant="bodyMd"><strong>Shop:</strong> {job.shop}</Text>
              <Text as="p" variant="bodyMd"><strong>Type:</strong> {formatJobName(job.type)}</Text>
              <Text as="p" variant="bodyMd"><strong>Status:</strong> {job.status}</Text>
              <Text as="p" variant="bodyMd"><strong>Created:</strong> {formatRelativeTime(job.createdAt)}</Text>
              {job.startedAt && <Text as="p" variant="bodyMd"><strong>Started:</strong> {formatRelativeTime(job.startedAt)}</Text>}
              {job.completedAt && <Text as="p" variant="bodyMd"><strong>Completed:</strong> {formatRelativeTime(job.completedAt)}</Text>}
              <Text as="p" variant="bodyMd"><strong>Retry Count:</strong> {job.retryCount} / {job.maxRetries}</Text>
              {job.errorMessage && <Text as="p" variant="bodyMd" tone="critical"><strong>Error:</strong> {job.errorMessage}</Text>}
              <BlockStack gap="200">
                <Text as="p" variant="bodyMd"><strong>Data:</strong></Text>
                <Button
                  size="slim"
                  onClick={() => handleCopy(JSON.stringify(JSON.parse(job.data), null, 2), "Job data copied to clipboard!")}
                >
                  Copy Data
                </Button>
                <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all', maxHeight: '300px', overflowY: 'auto', background: 'var(--p-color-bg-surface-secondary)', padding: 'var(--p-space-300)', borderRadius: 'var(--p-border-radius-100)' }}>
                  {JSON.stringify(JSON.parse(job.data), null, 2)}
                </pre>
              </BlockStack>
            </BlockStack>
          </Card>
        </Layout.Section>
        <Layout.Section>
          <InlineStack align="end">
            <Text as="p" variant="bodySm" tone="subdued">
              Last updated: <Text as="span" tone={isFlashing ? "success" : "subdued"}>{formattedTime}</Text>
            </Text>
          </InlineStack>
        </Layout.Section>
      </Layout>
      {toastActive && (
        <Toast content={toastContent} error={isToastError} onDismiss={handleToastDismiss} />
      )}
    </Page>
  );
}
