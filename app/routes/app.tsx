import type { HeadersFunction, LoaderFunctionArgs } from "@remix-run/node";
import {
  Link,
  Outlet,
  useLoaderData,
  useLocation,
  useRouteError,
} from "@remix-run/react";
import { boundary } from "@shopify/shopify-app-remix/server";
import { AppProvider } from "@shopify/shopify-app-remix/react";
import {
  HomeIcon,
  AutomationIcon,
  AppsIcon,
  OrdersStatusIcon as JobsIcon,
  SettingsIcon,
} from "@shopify/polaris-icons";
import polarisStyles from "@shopify/polaris/build/esm/styles.css?url";
import { Frame, Navigation } from "@shopify/polaris";

import { authenticate } from "../shopify.server";

export const links = () => [{ rel: "stylesheet", href: polarisStyles }];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  return { apiKey: process.env.SHOPIFY_API_KEY || "" };
};

const navLinks = [
  { to: "/app/dashboard", label: "Dashboard", icon: HomeIcon, rel: "home" },
  { to: "/app/automations", label: "My Automations", icon: AutomationIcon },
  { to: "/app/library", label: "Task Library", icon: AppsIcon },
  
  { to: "/app/jobs", label: "Job Status", icon: JobsIcon },
  { to: "/app/settings", label: "Settings", icon: SettingsIcon },
];

export default function App() {
  const { apiKey } = useLoaderData<typeof loader>();
  const location = useLocation();

  const navigationMarkup = (
    <Navigation location={location.pathname}>
      <Navigation.Section
        items={navLinks.map((link) => ({
          url: link.to,
          label: link.label,
          icon: link.icon,
          // The `Link` component from Remix is used here to ensure client-side routing.
          // It's passed to the `Navigation.Item` via the `component` prop.
          component: Link,
        }))}
      />
    </Navigation>
  );

  return (
    <AppProvider isEmbeddedApp apiKey={apiKey}>
      <Frame navigation={navigationMarkup}><Outlet /></Frame>
    </AppProvider>
  );
}

// Shopify needs Remix to catch some thrown responses, so that their headers are included in the response.
export function ErrorBoundary() {
  return boundary.error(useRouteError());
}

export const headers: HeadersFunction = (headersArgs) => {
  return boundary.headers(headersArgs);
};
