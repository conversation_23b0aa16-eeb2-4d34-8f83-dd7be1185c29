import { use<PERSON>allback, useEffect, useState } from "react";
import { redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigate, useNavigation } from "@remix-run/react";
import { Button, Card, Layout, Page, Text, TextField, FormLayout, Banner, Select } from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { JobType, type Automation, type Prisma } from "@prisma/client";
import { TASKS_LIBRARY } from "../data/tasks";
import { AutomationConfigDispatcher } from "../components/automation-configs/AutomationConfigDispatcher";

// Define a more specific type for the automation data used in this route
type TaskTemplate = typeof TASKS_LIBRARY[number];

// Type for the configuration options based on optionsSchema
export type AutomationOptionsConfig = Record<string, any>;

type AutomationConfigData = TaskTemplate &
  Partial<Pick<Automation, 'id' | 'name' | 'status'>> & {
    config?: AutomationOptionsConfig; // Parsed config object
  };

type LoaderData = {
  isNew: boolean;
  automation: AutomationConfigData;
};

type ActionData = {
  errors?: {
    name?: string;
    config?: string;
    general?: string;
  };
};

// LOADER: Fetches data and ensures a consistent object shape is always returned.
export const loader = async ({ request, params }: LoaderFunctionArgs): Promise<Response> => {
  const { session } = await authenticate.admin(request);
  const recordId = parseInt(params.id!, 10);

  if (isNaN(recordId)) {
    throw new Response("Invalid ID", { status: 400 });
  }

  // This function builds a default config object from a task's schema.
  const buildDefaultConfig = (task: TaskTemplate): AutomationOptionsConfig => {
    const config: AutomationOptionsConfig = {};
    if (task.optionsSchema) {
      task.optionsSchema.forEach(opt => {
        // Deep copy the default value to avoid mutation issues.
        config[opt.name] = JSON.parse(JSON.stringify(opt.defaultValue));
      });
    }
    return config;
  };

  // Try to find an existing automation by its own ID.
  const existingAutomation = await prisma.automation.findUnique({
    where: { id: recordId, shop: session.shop },
  });

  if (existingAutomation) {
    const taskTemplate = TASKS_LIBRARY.find(task => task.type === existingAutomation.type);
    if (!taskTemplate) {
      console.error(`Data inconsistency: Automation ID ${existingAutomation.id} for shop ${session.shop} has type ${existingAutomation.type} not found in TASKS_LIBRARY.`);
      throw new Response("Configuration data for this automation type is missing. Please contact support.", { status: 404 });
    }

    // Start with the default config for the template.
    const defaultConfig = buildDefaultConfig(taskTemplate);
    // Safely merge the saved config from the database.
    const dbConfig = existingAutomation.config as AutomationOptionsConfig | null;
    const finalConfig = { ...defaultConfig, ...(dbConfig || {}) };

    const automationData = { ...taskTemplate, ...existingAutomation, config: finalConfig };
    return Response.json({ isNew: false, automation: automationData });
  }

  // If not found, assume the ID is for a new task from the library.
  const taskTemplate = TASKS_LIBRARY.find(task => task.id === recordId);
  if (!taskTemplate) {
    throw new Response("Task template not found.", { status: 404 });
  }

  // For a new automation, always use the default config from the schema.
  const defaultConfig = buildDefaultConfig(taskTemplate);

  return Response.json({ isNew: true, automation: { ...taskTemplate, name: taskTemplate.title, config: defaultConfig } });
};

// ACTION: Handles saving (creating or updating) the automation.
export const action = async ({ request, params }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const _action = formData.get("_action") as "create" | "update" | null;
  const name = formData.get("name") as string | null;
  const type = formData.get("type") as JobType | null;
  const trigger = formData.get("trigger") as string | null;
  const configString = formData.get("config") as string | null;

  const automationRecordId = parseInt(params.id!, 10);

  if (!name || name.trim() === "") {
    return Response.json({ errors: { name: "Automation name is required." } }, { status: 400 });
  }

  let configData: Prisma.NullableJsonNullValueInput | Prisma.InputJsonValue | undefined = undefined;

  if (configString) {
    try {
      const parsed = JSON.parse(configString);

      // Type-specific filtering
      if (type === JobType.ORDER_COLLECTION_TAG && Array.isArray(parsed.collections_and_tags)) {
        parsed.collections_and_tags = parsed.collections_and_tags.filter(
          (p: { key: string; value: string }) =>
            p.key.trim() !== "" && p.value.trim() !== ""
        );
      }

      configData = parsed;
    } catch (error) {
      return Response.json({ errors: { config: "Invalid configuration format." } }, { status: 400 });
    }
  }

  try {
    if (_action === "create") {
      if (!type || !trigger) {
        return Response.json({ errors: { general: "Missing type or trigger for new automation." } }, { status: 400 });
      }
      await prisma.automation.create({
        data: { shop: session.shop, name, type, trigger, status: "Active", config: configData }
      });
    } else if (_action === "update") {
      await prisma.automation.update({
        where: { id: automationRecordId, shop: session.shop },
        data: { name, config: configData },
      });
    } else {
      return Response.json({ errors: { general: "Invalid action specified." } }, { status: 400 });
    }

    return redirect("/app/automations");
  } catch (error) {
    console.error(error);
    return Response.json({
      errors: {
        general: "Failed to save automation. Please try again."
      }
    }, { status: 500 });
  }
};

export default function ConfigureAutomation() {
  const { isNew, automation } = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();
  const navigate = useNavigate();
  const navigation = useNavigation();
  const isSaving = navigation.state === 'submitting';

  const [name, setName] = useState(automation.name);
  const [currentConfig, setCurrentConfig] = useState<AutomationOptionsConfig>(automation.config || {});
  const [delayType, setDelayType] = useState<string>(currentConfig.delay?.type || 'immediate');
  const [delayValue, setDelayValue] = useState<string>(currentConfig.delay?.value?.toString() || '');
  const [delayUnit, setDelayUnit] = useState<string>(currentConfig.delay?.unit || 'hours');
  const [timestamp, setTimestamp] = useState<string>(currentConfig.delay?.timestamp || '');

  useEffect(() => {
    setName(automation.name);
    setCurrentConfig(automation.config || {});
    setDelayType(automation.config?.delay?.type || 'immediate');
    setDelayValue(automation.config?.delay?.value?.toString() || '');
    setDelayUnit(automation.config?.delay?.unit || 'hours');
    setTimestamp(automation.config?.delay?.timestamp || '');
  }, [automation]);

  const handleConfigChange = useCallback((newConfig: any) => {
    setCurrentConfig(prevConfig => ({ ...prevConfig, ...newConfig }));
  }, []);

  const updateDelayConfig = useCallback((type: string, value: string, unit: string, timestamp: string) => {
    let delayConfig: any = { type };
    if (type === 'relative') {
      delayConfig.value = value ? parseInt(value, 10) : 0;
      delayConfig.unit = unit;
    } else if (type === 'specific') {
      delayConfig.timestamp = timestamp;
    }
    setCurrentConfig(prevConfig => ({
      ...prevConfig,
      delay: delayConfig
    }));
  }, []);

  const handleDelayTypeChange = useCallback((value: string) => {
    setDelayType(value);
    updateDelayConfig(value, delayValue, delayUnit, timestamp);
  }, [delayValue, delayUnit, timestamp, updateDelayConfig]);

  const handleDelayValueChange = useCallback((value: string) => {
    setDelayValue(value);
    updateDelayConfig(delayType, value, delayUnit, timestamp);
  }, [delayType, delayUnit, timestamp, updateDelayConfig]);

  const handleDelayUnitChange = useCallback((value: string) => {
    setDelayUnit(value);
    updateDelayConfig(delayType, delayValue, value, timestamp);
  }, [delayType, delayValue, timestamp, updateDelayConfig]);

  const handleTimestampChange = useCallback((value: string) => {
    setTimestamp(value);
    updateDelayConfig(delayType, delayValue, delayUnit, value);
  }, [delayType, delayValue, delayUnit, updateDelayConfig]);

  return (
    <Page
      title={isNew ? `Configure: ${automation.title}` : `Edit: ${automation.name}`}
      backAction={{ content: "Automations", onAction: () => navigate("/app/automations") }}
    >
      <Layout>
        <Layout.Section>
          <Banner title="Automation Setup" tone="info">
            <p>Give your automation a descriptive name. It will be triggered automatically based on its event type.</p>
            {actionData?.errors?.general && (
              <Text tone="critical" as="p">{actionData.errors.general}</Text>
            )}
            {actionData?.errors && (
              <Banner tone="critical">
                {Object.values(actionData.errors).map((err, i) => (
                  <Text key={i} as="p">{err}</Text>
                ))}
              </Banner>
            )}
          </Banner>
        </Layout.Section>
        <Layout.Section>
          <Card>
            <Form method="post">
              <input type="hidden" name="_action" value={isNew ? "create" : "update"} />
              <input type="hidden" name="type" value={automation.type} />
              <input type="hidden" name="trigger" value={automation.trigger} />
              <input type="hidden" name="config" value={JSON.stringify(currentConfig)} />

              <FormLayout>
                <TextField
                  label="Automation Name"
                  value={name}
                  onChange={setName}
                  name="name"
                  autoComplete="off"
                  helpText="This name is for your reference in the 'My Automations' list."
                  error={actionData?.errors?.name}
                />
                <Text variant="bodyMd" as="p" tone="subdued">
                  <strong>Trigger:</strong> {automation.trigger}
                </Text>
                <Text variant="bodyMd" as="p" tone="subdued">
                  <strong>Description:</strong> {automation.description}
                </Text>

                <Text variant="headingMd" as="h2">Scheduling</Text>
                <Select
                  label="Execution Timing"
                  options={[
                    { label: 'Immediate', value: 'immediate' },
                    { label: 'Delayed (Relative)', value: 'relative' },
                    { label: 'Scheduled (Specific Time)', value: 'specific' }
                  ]}
                  value={delayType}
                  onChange={handleDelayTypeChange}
                />
                {delayType === 'relative' && (
                  <FormLayout.Group>
                    <TextField
                      label="Delay Amount"
                      type="number"
                      value={delayValue}
                      onChange={handleDelayValueChange}
                      min="0"
                      autoComplete="off"
                    />
                    <Select
                      label="Delay Unit"
                      options={[
                        { label: 'Seconds', value: 'seconds' },
                        { label: 'Minutes', value: 'minutes' },
                        { label: 'Hours', value: 'hours' },
                        { label: 'Days', value: 'days' }
                      ]}
                      value={delayUnit}
                      onChange={handleDelayUnitChange}
                    />
                  </FormLayout.Group>
                )}
                {delayType === 'specific' && (
                  <TextField
                    label="Scheduled Date and Time (YYYY-MM-DDThh:mm)"
                    type="datetime-local"
                    value={timestamp}
                    onChange={handleTimestampChange}
                    autoComplete="off"
                  />
                )}

                <AutomationConfigDispatcher
                  type={automation.type}
                  config={currentConfig}
                  onConfigChange={handleConfigChange}
                />

                <Button submit variant="primary" loading={isSaving}>
                  {isNew ? "Add Automation" : "Save Changes"}
                </Button>
              </FormLayout>
            </Form>
          </Card>
        </Layout.Section>
      </Layout>
      <div style={{ paddingBottom: 'var(--p-space-800)' }} />
    </Page>
  );
}
