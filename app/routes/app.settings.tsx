import { type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useFetcher, useLoaderData } from "@remix-run/react";
import { useState, useEffect } from "react";
import {
  Card,
  Layout,
  Page,
  TextField,
  Button,
  BlockStack,
  Toast,
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";

// LOADER
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const appSettings = await prisma.appSetting.findUnique({
    where: { shop: session.shop },
  });
  return Response.json({ settings: appSettings?.settings });
};

// ACTION
export const action = async ({ request }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const timeout = formData.get("timeout");

  const parsedTimeout = parseInt(timeout as string, 10);

  if (isNaN(parsedTimeout) || parsedTimeout <= 0) {
    return Response.json({ error: "Timeout must be a positive number." }, { status: 400 });
  }

  await prisma.appSetting.upsert({
    where: { shop: session.shop },
    update: {
      settings: {
        stuckJobTimeout: parsedTimeout,
      },
    },
    create: {
      shop: session.shop,
      settings: {
        stuckJobTimeout: parsedTimeout,
      },
    },
  });

  return Response.json({ ok: true });
};

export default function Settings() {
  const { settings } = useLoaderData<{ settings: { stuckJobTimeout: number } }>();
  const fetcher = useFetcher<typeof action>();
  const [timeout, setTimeout] = useState(settings?.stuckJobTimeout?.toString() || "30");
  const [timeoutError, setTimeoutError] = useState<string | undefined>(undefined);

  const [toastContent, setToastContent] = useState("");
  const [isToastError, setIsToastError] = useState(false);
  const [toastActive, setToastActive] = useState(false);

  const handleToastDismiss = () => setToastActive(false);

  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data) {
      if ('error' in fetcher.data && fetcher.data.error) {
        setToastContent(fetcher.data.error);
        setIsToastError(true);
        setToastActive(true);
      } else if ('ok' in fetcher.data && fetcher.data.ok) {
        setToastContent("Settings saved successfully!");
        setIsToastError(false);
        setToastActive(true);
      }
    }
  }, [fetcher.data, fetcher.state]);

  const handleTimeoutChange = (value: string) => {
    setTimeout(value);
    if (value === "") {
      setTimeoutError("Timeout cannot be empty.");
    } else if (isNaN(parseInt(value, 10))) {
      setTimeoutError("Timeout must be a number.");
    } else if (parseInt(value, 10) <= 0) {
      setTimeoutError("Timeout must be a positive number.");
    } else {
      setTimeoutError(undefined);
    }
  };

  return (
    <Page title="Settings">
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="400">
              <fetcher.Form method="post">
                <TextField
                  label="Stuck Job Timeout (minutes)"
                  type="number"
                  name="timeout"
                  value={timeout}
                  onChange={handleTimeoutChange}
                  autoComplete="off"
                  error={timeoutError}
                  helpText="This setting determines how long a job can be in 'processing' status before it's automatically marked as 'failed'. A reasonable value prevents jobs from being stuck indefinitely."
                />
                <Button submit loading={fetcher.state !== 'idle'} disabled={fetcher.state !== 'idle' || !!timeoutError}>Save</Button>
              </fetcher.Form>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
      {toastActive && (
        <Toast content={toastContent} error={isToastError} onDismiss={handleToastDismiss} />
      )}
    </Page>
  );
}
