import { useState, useEffect } from "react";
import { type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Banner,
  Card,
  Layout,
  Page,
  ResourceList,
  ResourceItem,
  Text,
  Badge,
  BlockStack,
  InlineStack,
  Spinner, // Added Spinner
  EmptyState,
} from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { JobStatus, type JobType } from "@prisma/client"; // Added JobType
import {
  getStatusBadgeTone,
  formatJobName,
  formatRelativeTime,
} from "../utils/formatting"; // Import shared utilities
import { usePollingWithFeedback } from "app/hooks/usePollingWithFeedback";


// --- REMIX LOADER ---
// Fetches all the data needed for the dashboard from the database.
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const { shop } = session;

  // Calculate the date 30 days ago for the "Total Runs" metric
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  // Fetch all data points concurrently for better performance
  const [activeAutomations, totalRuns, recentActivity] = await Promise.all([
    // 1. Get count of active automations
    prisma.automation.count({
      where: { shop, status: "Active" },
    }),
    // 2. Get count of all jobs run in the last 30 days
    prisma.job.count({
      where: {
        shop,
        createdAt: { gte: thirtyDaysAgo },
      },
    }),
    // 3. Get the 5 most recent job activities
    prisma.job.findMany({
      where: { shop },
      orderBy: { createdAt: "desc" },
      take: 5,
    }),
  ]);

  return Response.json({
    stats: { activeAutomations, totalRuns },
    recentActivity,
  });
};

const EmptyActivityState = (
  <EmptyState
    heading="No recent activity to display"
    image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
  >
    <p>When your automations run, you'll see the latest activity here.</p>
  </EmptyState>
);


export default function Dashboard() {
  const { stats, recentActivity } = useLoaderData<typeof loader>();
  const [welcomeBannerDismissed, setWelcomeBannerDismissed] = useState(true); // Default to true

  // On component mount, check localStorage to see if the banner should be shown.
  useEffect(() => {
    const dismissed = localStorage.getItem("welcomeBannerDismissed");
    if (dismissed !== "true") {
      setWelcomeBannerDismissed(false);
    }
  }, []);

  const handleDismissWelcomeBanner = () => {
    setWelcomeBannerDismissed(true);
    localStorage.setItem("welcomeBannerDismissed", "true");
  };

  usePollingWithFeedback(30000);

  return (
    <Page title="Dashboard">
      <Layout>
        {!welcomeBannerDismissed && (
          <Layout.Section>
            <Banner
              title="Welcome to the Automation Engine"
              action={{ content: "Browse Task Library", url: "/app/library" }}
              onDismiss={handleDismissWelcomeBanner}
            >
              <p>Automate repetitive tasks in your store with our pre-built workflows. Get started by browsing the Task Library.</p>
            </Banner>
          </Layout.Section>
        )}

        <Layout.Section>
          <BlockStack gap="500">
            <Text variant="headingXl" as="h2">Key Metrics</Text>
            <InlineStack gap="400" wrap={true}>
              <Card padding="500">
                  <BlockStack gap="200">
                    <Text variant="headingMd" as="h2">Active Automations</Text>
                    <Text variant="heading2xl" as="h3">{stats.activeAutomations}</Text>
                    <Text variant="bodyMd" as="p" tone="subdued">Currently enabled tasks.</Text>
                  </BlockStack>
              </Card>
              <Card padding="500">
                  <BlockStack gap="200">
                    <Text variant="headingMd" as="h2">Total Runs (Last 30 Days)</Text>
                    <Text variant="heading2xl" as="h3">{stats.totalRuns}</Text>
                    <Text variant="bodyMd" as="p" tone="subdued">Executions in the past month.</Text>
                  </BlockStack>
              </Card>
            </InlineStack>
          </BlockStack>
        </Layout.Section>

        <Layout.Section>
          <BlockStack gap="500">
            <Text variant="headingXl" as="h2">Recent Activity</Text>
            <Card padding="0">
              <ResourceList
                resourceName={{ singular: "run", plural: "runs" }}
                items={recentActivity}
                emptyState={EmptyActivityState}
                renderItem={(item) => {
                  const { id, type, createdAt, status } = item;
                  return (
                    <ResourceItem
                      id={id.toString()}
                      accessibilityLabel={`View details for ${formatJobName(type as JobType)} run`}
                      onClick={() => { /* Can be enhanced to navigate to log details */ }}
                    >
                      <BlockStack gap="0">
                        <div style={{ padding: 'var(--p-space-400)' }}>
                          <InlineStack wrap={false} gap="200" blockAlign="center" align="space-between">
                            <BlockStack gap="050">
                              <Text variant="bodyMd" fontWeight="bold" as="h3">{formatJobName(type as JobType)}</Text>
                              <Text variant="bodySm" as="p" tone="subdued">{formatRelativeTime(createdAt)}</Text>
                            </BlockStack>
                            <InlineStack gap="200" align="center">
                              {status === JobStatus.processing && <Spinner accessibilityLabel="Task is currently running" size="small" />}
                              <Badge tone={getStatusBadgeTone(status as JobStatus)}>{status}</Badge>
                            </InlineStack>
                          </InlineStack>
                        </div>
                      </BlockStack>
                    </ResourceItem>
                  );
                }}
              />
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
      {/* Add some spacing at the bottom of the page */}
      <div style={{ paddingBottom: 'var(--p-space-800)' }} />
    </Page>
  );
}