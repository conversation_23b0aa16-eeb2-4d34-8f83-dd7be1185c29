import type { ActionFunctionArgs } from "@remix-run/node";
import { JobType, JobStatus } from "@prisma/client"; // Import JobStatus
import { authenticate } from "../shopify.server";
import { createAutomationJob } from "../services/automations.server";
import { logToFile } from "../utils/logger.server";
import prisma from "../db.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  const { topic, shop, admin, payload } = await authenticate.webhook(request);

  if (!admin) {
    throw new Response("Unauthorized", { status: 401 });
  }

  switch (topic) {
    case "ORDERS_CREATE":
    case "ORDERS_UPDATED":
      logToFile("webhook-orders-cart-attribute-tagging", "info", `Received ${topic} webhook for shop: ${shop}`);
      try {
        const activeAutomation = await prisma.automation.findFirst({
          where: {
            shop,
            type: JobType.ORDER_CART_ATTRIBUTE_TAG,
            status: "Active",
          },
        });

        if (!activeAutomation) {
          logToFile("webhook-orders-cart-attribute-tagging", "info", `No active ORDER_CART_ATTRIBUTE_TAG automation found for shop ${shop}. Skipping job creation.`);
          return new Response("Automation not active", { status: 200 });
        }

        const orderGid = payload.admin_graphql_api_id;
        if (!orderGid) {
            logToFile("webhook-orders-cart-attribute-tagging", "error", `Missing admin_graphql_api_id in webhook payload for shop: ${shop}`);
            return new Response("Missing admin_graphql_api_id", { status: 400 });
        }

        // --- DE-DUPLICATION LOGIC ---
        // Check if a pending or processing job for this specific order already exists.
        const existingJob = await prisma.job.findFirst({
          where: {
            shop,
            type: JobType.ORDER_CART_ATTRIBUTE_TAG,
            data: {
              contains: `"${orderGid}"`,
            },
            status: {
              in: [JobStatus.pending, JobStatus.processing, JobStatus.scheduled],
            },
          },
        });

        if (existingJob) {
          logToFile("webhook-orders-cart-attribute-tagging", "info", `Skipping duplicate job enqueue for order ID: ${orderGid}. A job already exists with status: ${existingJob.status}.`);
          return new Response("Duplicate job request", { status: 200 });
        }
        // --- END DE-DUPLICATION LOGIC ---

        // --- LEAN PAYLOAD EXTRACTION ---
        const leanOrderData = {
          admin_graphql_api_id: payload.admin_graphql_api_id,
          tags: payload.tags,
          note_attributes: payload.note_attributes,
        };

        const jobData = {
          order: leanOrderData,
          config: activeAutomation.config ?? {},
        };
        // --- END LEAN PAYLOAD EXTRACTION ---

        await createAutomationJob(shop, JobType.ORDER_CART_ATTRIBUTE_TAG, jobData);

        logToFile("webhook-orders-cart-attribute-tagging", "info", `Successfully enqueued ORDER_CART_ATTRIBUTE_TAG job for order ID: ${payload.id} for shop: ${shop}`);
        return new Response("Job enqueued successfully", { status: 200 });

      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logToFile("webhook-orders-cart-attribute-tagging", "error", `Failed to enqueue job for ${topic} webhook for shop: ${shop}: ${errorMessage}`);
        return new Response("Failed to enqueue job", { status: 500 });
      }

    default:
      logToFile("webhook-orders-cart-attribute-tagging", "warn", `Unhandled webhook topic: ${topic} for shop: ${shop}`);
      throw new Response("Unhandled webhook topic", { status: 404 });
  }
};