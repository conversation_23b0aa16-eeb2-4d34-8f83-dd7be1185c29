import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";

interface OrderCustomerTagConfig {
  order_tags_and_customer_tags: { key: string; value: string }[];
}

// Job data can now contain the lean order object or just the ID for bulk runs
interface JobData {
  orderId: string; // Shopify Order GID, e.g., "gid://shopify/Order/1234567890"
  config: OrderCustomerTagConfig;
}

/**
 * Handles the job for tagging a customer based on order tags.
 *
 * @param job The job object from the database.
 * @param admin The authenticated Shopify Admin API client.
 * @returns A promise that resolves to true on success, false on failure.
 */
export async function handleCustomerTaggingJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = job.id;
  try {
    // 1. --- PARSE AND VALIDATE JOB DATA ---
    logToFile(jobId, "info", "Starting processing of customer tagging job.");
    const jobData: JobData = JSON.parse(job.data as string);
    const { orderId, config } = jobData;
    const tagMapping = config?.order_tags_and_customer_tags;

    if (!orderId) {
      logToFile(jobId, "error", `Invalid job data: Missing orderId.`);
      return false;
    }
    if (!tagMapping || tagMapping.length === 0) {
      logToFile(jobId, "error", `Invalid job data: Missing or empty tag mapping. Config: ${JSON.stringify(config)}`);
      return false;
    }

    // 2. --- GET ORDER AND CUSTOMER DETAILS FROM SHOPIFY ---
    logToFile(jobId, "info", `Fetching data for order ${orderId}.`);

    const getOrderAndCustomerTagsQuery = `#graphql
      query getOrderAndCustomerTags($orderId: ID!) {
        order(id: $orderId) {
          id
          tags
          customer {
            id
            tags
          }
        }
      }`;
    
    const response = await shopifyGraphqlRequest(admin, getOrderAndCustomerTagsQuery, { orderId }, jobId);
    const order = response.data?.order;

    if (!order) {
      logToFile(jobId, "warn", `Order ${orderId} not found. Skipping.`);
      return true; // Not a retryable error.
    }
    
    if (!order.customer) {
        logToFile(jobId, "info", `Order ${orderId} has no associated customer. Skipping.`);
        return true; // Not a retryable error.
    }
    
    const customerId = order.customer.id;
    logToFile(jobId, "info", `Processing order ${orderId} for customer ${customerId}.`);

    // 3. --- DETERMINE TAGS TO APPLY ---
    const orderTags = new Set(order.tags || []);
    const customerTags = new Set(order.customer.tags || []);
    const customerTagsToApply = tagMapping
      .filter(pair => orderTags.has(pair.key) && !customerTags.has(pair.value))
      .map(pair => pair.value);

    if (customerTagsToApply.length === 0) {
      logToFile(jobId, "info", `No new tags to apply to customer ${customerId}. Job complete.`);
      return true;
    }

    // 4. --- APPLY THE TAGS ---
    logToFile(jobId, "info", `Adding tags [${customerTagsToApply.join(", ")}] to customer ${customerId}.`);

    const addTagMutation = `#graphql
      mutation tagsAdd($id: ID!, $tags: [String!]!) {
        tagsAdd(id: $id, tags: $tags) {
          node { id }
          userErrors { field message }
        }
      }`;

    const mutationResponse = await shopifyGraphqlRequest(admin, addTagMutation, { id: customerId, tags: customerTagsToApply }, jobId);
    const userErrors = mutationResponse.data?.tagsAdd?.userErrors;

    if (userErrors && userErrors.length > 0) {
      logToFile(jobId, "error", `Failed to add tags to customer ${customerId}: ${JSON.stringify(userErrors)}`);
      return false; // Fail the job so it can be retried.
    }

    logToFile(jobId, "info", `Successfully added tags to customer ${customerId}.`);
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(jobId, "error", `An unexpected error occurred: ${errorMessage}`);
    return false; // Let the worker handle the failure.
  }
}