import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import { parseJobData, getJobId } from "../utils/job-data";
import {
  processCustomerTagging,
  validateCustomerTagConfig,
  createCustomerTaggingContext,
  type CustomerTagConfig
} from "../utils/customer-tagging";
import { addCustomerTags } from "../utils/shopify-customers";

interface JobData {
  orderId: string; // Shopify Order GID, e.g., "gid://shopify/Order/1234567890"
  config: CustomerTagConfig;
}

// 🦠 BACTERIAL UTILITIES - Small, focused helper functions
const logger = {
  info: (jobId: string, message: string) => logToFile(jobId, "info", message),
  error: (jobId: string, message: string) => logToFile(jobId, "error", message),
  warn: (jobId: string, message: string) => logToFile(jobId, "warn", message),
};

// 🦠 BACTERIAL FUNCTION - Validate customer tagging job data
const validateJobData = (jobData: any): { isValid: boolean; error?: string; data?: JobData } => {
  if (!jobData.orderId) {
    return { isValid: false, error: "Missing orderId" };
  }

  const configValidation = validateCustomerTagConfig(jobData.config);
  if (!configValidation.isValid) {
    return { isValid: false, error: configValidation.error };
  }

  return { isValid: true, data: jobData as JobData };
};

export async function handleCustomerTaggingJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = getJobId(job);

  try {
    logger.info(jobId, "Starting customer tagging job");

    // 🦠 BACTERIAL APPROACH - Parse and validate job data
    const parseResult = parseJobData<JobData>(job.data as string, { orderId: 'string', config: 'object' });
    if (!parseResult.isValid) {
      logger.error(jobId, `Invalid job data: ${parseResult.error}`);
      return false;
    }

    const validationResult = validateJobData(parseResult.data);
    if (!validationResult.isValid) {
      logger.error(jobId, `Job data validation failed: ${validationResult.error}`);
      return false;
    }

    const { orderId, config } = validationResult.data!;
    logger.info(jobId, `Processing customer tagging for order: ${orderId}`);

    // 🦠 BACTERIAL APPROACH - Process customer tagging using focused utility
    const taggingResult = await processCustomerTagging(admin, orderId, config);

    if (!taggingResult.success) {
      logger.error(jobId, `Customer tagging processing failed: ${taggingResult.reason}`);
      return false;
    }

    // Handle cases where no action is needed
    if (taggingResult.reason) {
      logger.info(jobId, taggingResult.reason);
      return true;
    }

    const { customerId, tagsToApply } = taggingResult;

    if (!tagsToApply || tagsToApply.length === 0) {
      logger.info(jobId, `No new tags to apply to customer ${customerId}`);
      return true;
    }

    // 🦠 BACTERIAL APPROACH - Apply tags using focused utility
    const context = createCustomerTaggingContext(orderId, customerId, tagsToApply);
    logger.info(jobId, `Adding tags to customer: ${JSON.stringify(context)}`);

    const tagResult = await addCustomerTags(admin, customerId!, tagsToApply);

    if (!tagResult.success) {
      logger.error(jobId, `Failed to add tags to customer ${customerId}: ${JSON.stringify(tagResult.errors)}`);
      return false;
    }

    logger.info(jobId, `Successfully added tags to customer ${customerId}`);
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(jobId, `Customer tagging job failed: ${errorMessage}`);
    return false;
  }
}