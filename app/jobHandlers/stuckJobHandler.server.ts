import prisma from "../db.server";
import { logToFile } from "../utils/logger.server";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import {
  getAppSettings,
  processStuckJobCleanup,
  createCleanupContext
} from "../utils/job-cleanup";

// 🦠 BACTERIAL UTILITIES - Small, focused helper functions
const logger = {
  info: (context: string, message: string) => logToFile(context, "info", message),
  warn: (context: string, message: string) => logToFile(context, "warn", message),
  error: (context: string, message: string) => logToFile(context, "error", message),
};

export async function handleStuckJobCleanup(job: any, admin: any): Promise<boolean> {
  try {
    logger.info("stuck-job-cleanup", "Starting stuck job cleanup process");

    // 🦠 BACTERIAL APPROACH - Get app settings using focused utility
    const appSettings = await getAppSettings(prisma);

    // 🦠 BACTERIAL APPROACH - Process cleanup using focused utility
    const result = await processStuckJobCleanup(prisma, appSettings, logger);

    if (!result.success) {
      logger.error("stuck-job-cleanup", `Cleanup failed: ${result.error}`);
      return false;
    }

    const context = createCleanupContext(
      appSettings?.settings?.stuckJobTimeout || 30,
      result.processedCount
    );
    logger.info("stuck-job-cleanup", `Cleanup completed: ${JSON.stringify(context)}`);

    return true;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("stuck-job-cleanup", `Unexpected error in stuck job cleanup: ${errorMessage}`);
    return false;
  }
}
