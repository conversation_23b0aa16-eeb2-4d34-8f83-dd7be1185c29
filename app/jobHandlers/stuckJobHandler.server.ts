import prisma from "../db.server";
import { logToFile } from "../utils/logger.server";

const DEFAULT_STUCK_JOB_TIMEOUT_MINUTES = 30;

export async function handleStuckJobCleanup(job: any, admin: any): Promise<boolean> {
  const appSettings = await prisma.appSetting.findFirst();

  let timeoutMinutes: number = DEFAULT_STUCK_JOB_TIMEOUT_MINUTES;

  if (
    appSettings &&
    typeof appSettings.settings === 'object' &&
    appSettings.settings !== null &&
    'stuckJobTimeout' in appSettings.settings &&
    typeof (appSettings.settings as any).stuckJobTimeout === 'number'
  ) {
    timeoutMinutes = (appSettings.settings as any).stuckJobTimeout;
  }

  const timeoutMs = timeoutMinutes * 60 * 1000;
  const timeout = new Date(Date.now() - timeoutMs);

  try {
    const stuckJobs = await prisma.job.findMany({
      where: {
        status: "processing",
        startedAt: {
          lt: timeout,
        },
      },
    });

    if (stuckJobs.length === 0) {
      logToFile("stuck-job-cleanup", "info", "No stuck jobs found.");
      return true;
    }

    for (const job of stuckJobs) {
      await prisma.job.update({
        where: { id: job.id },
        data: {
          status: "failed",
          errorMessage: `Job timed out and was marked as failed by the system. It was in processing for more than ${timeoutMinutes} minutes.`,
        },
      });
      logToFile("stuck-job-cleanup", "warn", `Marked job ${job.id} as failed due to timeout.`);
    }
    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile("stuck-job-cleanup", "error", `Error during stuck job cleanup: ${errorMessage}`);
    return false;
  }
}
