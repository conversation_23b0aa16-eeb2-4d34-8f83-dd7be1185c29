import type { Job } from "@prisma/client";
import { updateJobStatus } from "../queues/jobQueue.server";
import { logToFile } from "../utils/logger.server";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";

/**
 * For a given collection, determines which sales channels it should be published on.
 * A channel is considered "publishable" if it contains at least one active, in-stock product
 * that is also published to that same channel.
 *
 * @param admin The Shopify Admin API client.
 * @param collectionId The GID of the collection to analyze.
 * @param jobId The ID of the job for logging purposes.
 * @returns A Promise that resolves to a Map where keys are publication GIDs and values are `true`,
 *          indicating the channel should have the collection published.
 */
async function getCollectionPublishStatusByChannel(
  admin: ShopifyUnAuthenticatedAdminClient,
  collectionId: string,
  jobId: string
): Promise<Map<string, boolean>> {
  const publishableChannels = new Map<string, boolean>();
  let cursor: string | null = null;
  let hasNextPage = true;

  while (hasNextPage) {
    const query = `#graphql
      query collectionProductsWithPublications($id: ID!, $first: Int!, $after: String) {
        collection(id: $id) {
          products(first: $first, after: $after) {
            pageInfo { hasNextPage endCursor }
            edges {
              node {
                status
                totalInventory
                resourcePublications(first: 50) {
                  edges {
                    node {
                      isPublished
                      publication { id }
                    }
                  }
                }
              }
            }
          }
        }
      }`;

    const response = await shopifyGraphqlRequest(admin, query, { id: collectionId, first: 100, after: cursor }, jobId);
    const products = response.data?.collection?.products?.edges || [];

    for (const productEdge of products) {
      const product = productEdge.node;

      // Only consider active, in-stock products
      if (product.status !== "ACTIVE" || product.totalInventory <= 0) {
        continue;
      }

      // This product is in stock. Mark all its published channels as "live".
      for (const pubEdge of product.resourcePublications.edges) {
        if (pubEdge.node.isPublished) {
          publishableChannels.set(pubEdge.node.publication.id, true);
        }
      }
    }

    hasNextPage = response.data?.collection?.products?.pageInfo?.hasNextPage || false;
    cursor = response.data?.collection?.products?.pageInfo?.endCursor || null;
  }

  logToFile(jobId, "info", `Found ${publishableChannels.size} publishable channels for collection ${collectionId}.`);

  return publishableChannels;
}

/**
 * Main handler for the bulk collection visibility update job.
 * Iterates through all collections and updates their published status on a per-channel basis.
 */
export async function handleCollectionVisibilityBulkUpdateJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = job.id;
  try {
    logToFile(jobId, "info", "Starting robust bulk processing of collection visibility.");

    // 1. --- FETCH ALL PUBLICATIONS (SALES CHANNELS) ---
    let allPublications: any[] = [];
    let pubCursor: string | null = null;
    let pubHasNextPage = true;
    logToFile(jobId, "info", "Fetching all publications (sales channels) from the store.");
    while(pubHasNextPage) {
        const pubQuery = `#graphql
            query getAllPublications($first: Int!, $after: String) {
                publications(first: $first, after: $after) {
                    pageInfo { hasNextPage endCursor }
                    edges {
                        node {
                            id
                            name
                        }
                    }
                }
            }`;
        const pubResponse = await shopifyGraphqlRequest(admin, pubQuery, { first: 100, after: pubCursor }, jobId);
        const pubsData = pubResponse.data?.publications;
        if (pubsData?.edges) {
            allPublications = allPublications.concat(pubsData.edges.map((edge: any) => edge.node));
        }
        pubHasNextPage = pubsData?.pageInfo?.hasNextPage || false;
        pubCursor = pubsData?.pageInfo?.endCursor || null;
    }
    logToFile(jobId, "info", `Found ${allPublications.length} total publications.`);

    // 2. --- PAGINATE THROUGH ALL COLLECTIONS ---
    let allCollections: any[] = [];
    let cursor: string | null = null;
    let hasNextPage = true;
    logToFile(jobId, "info", "Fetching all collections from the store.");
    while (hasNextPage) {
      const query = `#graphql
        query getAllCollections($first: Int!, $after: String) {
          collections(first: $first, after: $after) {
            pageInfo { hasNextPage endCursor }
            edges {
              node {
                id
                title
                resourcePublications(first: 50) {
                  edges {
                    node {
                      isPublished
                      publication { id name }
                    }
                  }
                }
              }
            }
          }
        }`;
      const response = await shopifyGraphqlRequest(admin, query, { first: 100, after: cursor }, jobId);
      const collectionsData = response.data?.collections;

      if (collectionsData?.edges) {
        allCollections = allCollections.concat(collectionsData.edges.map((edge: any) => edge.node));
      }
      hasNextPage = collectionsData?.pageInfo?.hasNextPage || false;
      cursor = collectionsData?.pageInfo?.endCursor || null;
    }
    logToFile(jobId, "info", `Found ${allCollections.length} total collections to process.`);

    // 3. --- PROCESS EACH COLLECTION ---
    for (const collection of allCollections) {
      logToFile(jobId, "info", `Processing collection: "${collection.title}" (ID: ${collection.id})`);

      // Determine which channels should have this collection published based on product stock.
      const publishStatusByChannel = await getCollectionPublishStatusByChannel(admin, collection.id, jobId);
      logToFile(jobId, "info", `Publishable channels for "${collection.title}": ${JSON.stringify(Object.fromEntries(publishStatusByChannel), null, 2)}`);

      // Build a map of the collection's current publication statuses for quick lookup.
      const currentPublicationStatus = new Map<string, boolean>();
      for (const pubEdge of collection.resourcePublications.edges) {
        currentPublicationStatus.set(pubEdge.node.publication.id, pubEdge.node.isPublished);
      }

      // 4. --- UPDATE PUBLICATION STATUS FOR EACH SALES CHANNEL ---
      // Iterate over all available sales channels in the store, not just the ones the collection is already on.
      for (const publication of allPublications) {
        const publicationId = publication.id;
        const publicationName = publication.name;
        
        const shouldBePublished = publishStatusByChannel.get(publicationId) || false;
        const isCurrentlyPublished = currentPublicationStatus.get(publicationId) || false;

        let actionToTake = "None";

        if (shouldBePublished && !isCurrentlyPublished) {
          actionToTake = "Publish";
          const mutation = `#graphql
            mutation PublishablePublish($id: ID!, $publicationId: ID!) {
              publishablePublish(id: $id, input: {publicationId: $publicationId}) {
                publishable {
                  publishedOnPublication(publicationId: $publicationId)
                }
                userErrors {
                  field
                  message
                }
              }
            }`;
          await shopifyGraphqlRequest(admin, mutation, { id: collection.id, publicationId: publicationId }, jobId);

        } else if (!shouldBePublished && isCurrentlyPublished) {
          actionToTake = "Unpublish";
          const mutation = `#graphql
            mutation PublishableUnpublish($id: ID!, $publicationId: ID!) {
              publishableUnpublish(id: $id, input: {publicationId: $publicationId}) {
                publishable {
                  publishedOnPublication(publicationId: $publicationId)
                }
                userErrors {
                  field
                  message
                }
              }
            }`;
          await shopifyGraphqlRequest(admin, mutation, { id: collection.id, publicationId: publicationId }, jobId);
        }
        
        if (actionToTake !== "None") {
           logToFile(jobId, "info", `Collection "${collection.title}" on channel "${publicationName}": Should be published: ${shouldBePublished}, Currently: ${isCurrentlyPublished}. Action: ${actionToTake}`);
        }
      }
    }

    logToFile(jobId, "info", "Bulk collection visibility update completed successfully.");
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(jobId, "error", `Bulk processing failed: ${errorMessage}`);
    await updateJobStatus(jobId, "failed", errorMessage);
    return false;
  }
}