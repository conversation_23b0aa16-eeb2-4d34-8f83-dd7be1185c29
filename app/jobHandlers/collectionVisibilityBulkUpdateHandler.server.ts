import type { Job } from "@prisma/client";
import { logToFile } from "../utils/logger.server";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import { getJobId } from "../utils/job-data";
import {
  fetchAllPublications,
  fetchAllCollections,
  processCollectionVisibility
} from "../utils/bulk-collection-visibility";

// 🦠 BACTERIAL UTILITIES - Small, focused helper functions
const logger = {
  info: (jobId: string, message: string) => logToFile(jobId, "info", message),
  error: (jobId: string, message: string) => logToFile(jobId, "error", message),
  warn: (jobId: string, message: string) => logToFile(jobId, "warn", message),
};

export async function handleCollectionVisibilityBulkUpdateJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = getJobId(job);

  try {
    logger.info(jobId, "Starting bulk collection visibility update job");

    // 🦠 BACTERIAL APPROACH - Fetch all publications using focused utility
    logger.info(jobId, "Fetching all publications (sales channels) from the store");
    const allPublications = await fetchAllPublications(
      admin,
      (count) => logger.info(jobId, `Found ${count} publications so far`)
    );
    logger.info(jobId, `Found ${allPublications.length} total publications`);

    // 🦠 BACTERIAL APPROACH - Fetch all collections using focused utility
    logger.info(jobId, "Fetching all collections from the store");
    const allCollections = await fetchAllCollections(
      admin,
      (count) => logger.info(jobId, `Found ${count} collections so far`)
    );
    logger.info(jobId, `Found ${allCollections.length} total collections to process`);

    // 🦠 BACTERIAL APPROACH - Process each collection using focused utility
    for (const collection of allCollections) {
      await processCollectionVisibility(
        admin,
        collection,
        allPublications,
        logger,
        jobId
      );
    }

    logger.info(jobId, "Bulk collection visibility update completed successfully");
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(jobId, `Bulk collection visibility update failed: ${errorMessage}`);
    return false;
  }
}