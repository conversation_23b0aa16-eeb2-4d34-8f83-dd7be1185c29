import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";

// --- INTERFACES ---

interface AutoTagCustomerByVendorConfig {
  // No specific configuration needed for now
}

interface JobData {
  orderId: string; // Shopify Order GID, e.g., "gid://shopify/Order/1234567890"
  config: AutoTagCustomerByVendorConfig;
}

// --- GRAPHQL RESPONSE TYPES for Type Safety ---

interface PageInfo {
  hasNextPage: boolean;
  endCursor: string | null;
}

interface LineItemsConnection {
  edges: {
    node: {
      vendor: string | null;
    };
  }[];
  pageInfo: PageInfo;
}

// Type for the initial query that fetches the customer and the first page of items
interface OrderAndFirstPageQueryResponse {
  order: {
    id: string;
    customer: {
      id: string;
      tags: string[];
    } | null;
    lineItems: LineItemsConnection;
  } | null;
}

// Type for subsequent paginated queries that only fetch more line items
interface SubsequentLineItemsQueryResponse {
    order: {
        lineItems: LineItemsConnection;
    } | null;
}

// Type for the mutation response
interface TagsAddMutationResponse {
  tagsAdd: {
    node: { id: string } | null;
    userErrors: {
      field: string[];
      message: string;
    }[];
  } | null;
}


export async function customerVendorTaggingHandler(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = job.id;
  try {
    // 1. --- PARSE AND VALIDATE JOB DATA ---
    logToFile(jobId, "info", "Starting processing of customer vendor tagging job.");

    if (!job.data) {
      logToFile(jobId, "error", "Invalid job data: Job data is missing.");
      return false;
    }
    const jobData: JobData = JSON.parse(job.data as string);
    const { orderId } = jobData;

    if (!orderId) {
      logToFile(jobId, "error", `Invalid job data: Missing orderId.`);
      return false;
    }

    // 2. --- GET ORDER, CUSTOMER, AND ALL LINE ITEMS (WITH PAGINATION) ---
    logToFile(jobId, "info", `Fetching data for order ${orderId}.`);

    const allVendors = new Set<string>();

    // --- Initial Query: Get customer and first page of line items ---
    const getOrderAndFirstPageQuery = `#graphql
      query getOrderAndCustomer($orderId: ID!) {
        order(id: $orderId) {
          id
          customer { id tags }
          lineItems(first: 250) {
            edges { node { vendor } }
            pageInfo { hasNextPage endCursor }
          }
        }
      }`;

    // FIX: Use type assertion on the result of the await expression
    const initialResponse = (await shopifyGraphqlRequest(admin, getOrderAndFirstPageQuery, { orderId }, jobId)) as { data: OrderAndFirstPageQueryResponse };
    const order = initialResponse.data?.order;

    if (!order) {
      logToFile(jobId, "warn", `Order ${orderId} not found. Skipping.`);
      return true;
    }

    if (!order.customer) {
      logToFile(jobId, "info", `Order ${orderId} has no associated customer. Skipping.`);
      return true;
    }

    const customerId = order.customer.id;
    const customerTags = new Set(order.customer.tags || []);
    logToFile(jobId, "info", `Processing order ${orderId} for customer ${customerId}.`);

    let lineItemsConnection = order.lineItems;
    let hasNextPage = lineItemsConnection.pageInfo.hasNextPage;
    let cursor = lineItemsConnection.pageInfo.endCursor;

    // --- Process first page of vendors ---
    // (No 'any' errors here now, because 'lineItemsConnection' is strongly typed)
    lineItemsConnection.edges
      .map(edge => edge.node.vendor)
      .filter((vendor): vendor is string => !!vendor)
      .forEach(vendor => allVendors.add(vendor));

    // --- Paginated Queries: Loop if more line items exist ---
    if (hasNextPage) {
        logToFile(jobId, "info", `Order ${orderId} has more than 250 line items. Starting pagination.`);
    }

    const getSubsequentLineItemsQuery = `#graphql
        query getSubsequentLineItems($orderId: ID!, $after: String!) {
            order(id: $orderId) {
                lineItems(first: 250, after: $after) {
                    edges { node { vendor } }
                    pageInfo { hasNextPage, endCursor }
                }
            }
        }`;

    while (hasNextPage && cursor) {
      // FIX: Use type assertion on the result
      const paginatedResponse = (await shopifyGraphqlRequest(admin, getSubsequentLineItemsQuery, { orderId, after: cursor }, jobId)) as { data: SubsequentLineItemsQueryResponse };
      const nextLineItems = paginatedResponse.data?.order?.lineItems;

      if (!nextLineItems) {
        logToFile(jobId, "warn", `Failed to fetch a subsequent page of line items for order ${orderId}. Proceeding with vendors found so far.`);
        break;
      }

      nextLineItems.edges
        .map(edge => edge.node.vendor)
        .filter((vendor): vendor is string => !!vendor)
        .forEach(vendor => allVendors.add(vendor));

      hasNextPage = nextLineItems.pageInfo.hasNextPage;
      cursor = nextLineItems.pageInfo.endCursor;
    }

    // 3. --- DETERMINE TAGS TO APPLY ---
    const tagsToAdd = [...allVendors].filter(vendor => !customerTags.has(vendor));

    if (tagsToAdd.length === 0) {
      logToFile(jobId, "info", `No new vendor tags to apply to customer ${customerId}. Job complete.`);
      return true;
    }

    // 4. --- APPLY THE TAGS ---
    logToFile(jobId, "info", `Adding tags [${tagsToAdd.join(", ")}] to customer ${customerId}.`);

    const addTagMutation = `#graphql
      mutation tagsAdd($id: ID!, $tags: [String!]!) {
        tagsAdd(id: $id, tags: $tags) {
          node { id }
          userErrors { field message }
        }
      }`;

    // FIX: Use type assertion on the result
    const mutationResponse = (await shopifyGraphqlRequest(admin, addTagMutation, { id: customerId, tags: tagsToAdd }, jobId)) as { data: TagsAddMutationResponse };
    const userErrors = mutationResponse.data?.tagsAdd?.userErrors;

    if (userErrors && userErrors.length > 0) {
      logToFile(jobId, "error", `Failed to add tags to customer ${customerId}: ${JSON.stringify(userErrors)}`);
      return false;
    }

    logToFile(jobId, "info", `Successfully added tags to customer ${customerId}.`);
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(jobId, "error", `An unexpected error occurred in customerVendorTaggingHandler: ${errorMessage}`);
    return false;
  }
}