import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import { parseJobData, getJobId } from "../utils/job-data";
import { addCustomerTags } from "../utils/shopify-customers";
import {
  processVendorTagging,
  validateVendorTaggingConfig,
  createVendorTaggingContext,
  type VendorTaggingConfig
} from "../utils/vendor-tagging";

interface JobData {
  orderId: string; // Shopify Order GID, e.g., "gid://shopify/Order/1234567890"
  config: VendorTaggingConfig;
}

// 🦠 BACTERIAL UTILITIES - Small, focused helper functions
const logger = {
  info: (jobId: string, message: string) => logToFile(jobId, "info", message),
  error: (jobId: string, message: string) => logToFile(jobId, "error", message),
  warn: (jobId: string, message: string) => logToFile(jobId, "warn", message),
};

// 🦠 BACTERIAL FUNCTION - Validate vendor tagging job data
const validateJobData = (jobData: any): { isValid: boolean; error?: string; data?: JobData } => {
  if (!jobData.orderId) {
    return { isValid: false, error: "Missing orderId" };
  }

  const configValidation = validateVendorTaggingConfig(jobData.config);
  if (!configValidation.isValid) {
    return { isValid: false, error: configValidation.error };
  }

  return { isValid: true, data: jobData as JobData };
};

export async function customerVendorTaggingHandler(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = getJobId(job);

  try {
    logger.info(jobId, "Starting customer vendor tagging job");

    // 🦠 BACTERIAL APPROACH - Parse and validate job data
    const parseResult = parseJobData<JobData>(job.data as string, { orderId: 'string', config: 'object' });
    if (!parseResult.isValid) {
      logger.error(jobId, `Invalid job data: ${parseResult.error}`);
      return false;
    }

    const validationResult = validateJobData(parseResult.data);
    if (!validationResult.isValid) {
      logger.error(jobId, `Job data validation failed: ${validationResult.error}`);
      return false;
    }

    const { orderId, config } = validationResult.data!;
    logger.info(jobId, `Processing vendor tagging for order: ${orderId}`);

    // 🦠 BACTERIAL APPROACH - Process vendor tagging using focused utility
    const taggingResult = await processVendorTagging(
      admin,
      orderId,
      config,
      (vendorCount) => logger.info(jobId, `Found ${vendorCount} unique vendors so far`)
    );

    if (!taggingResult.success) {
      logger.error(jobId, `Vendor tagging processing failed: ${taggingResult.reason}`);
      return false;
    }

    // Handle cases where no action is needed
    if (taggingResult.reason) {
      logger.info(jobId, taggingResult.reason);
      return true;
    }

    const { customerId, vendorTags } = taggingResult;

    if (!vendorTags || vendorTags.length === 0) {
      logger.info(jobId, `No new vendor tags to apply to customer ${customerId}`);
      return true;
    }

    // 🦠 BACTERIAL APPROACH - Apply tags using focused utility
    const context = createVendorTaggingContext(orderId, customerId, vendorTags.length, vendorTags);
    logger.info(jobId, `Adding vendor tags to customer: ${JSON.stringify(context)}`);

    const tagResult = await addCustomerTags(admin, customerId!, vendorTags);

    if (!tagResult.success) {
      logger.error(jobId, `Failed to add vendor tags to customer ${customerId}: ${JSON.stringify(tagResult.errors)}`);
      return false;
    }

    logger.info(jobId, `Successfully added vendor tags to customer ${customerId}`);
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(jobId, `Customer vendor tagging job failed: ${errorMessage}`);
    return false;
  }
}