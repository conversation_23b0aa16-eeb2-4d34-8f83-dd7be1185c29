import { logToFile } from "../utils/logger.server";
import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import { parseJobData, getJobId } from "../utils/job-data";
import { addOrderTags } from "../utils/shopify-orders";
import {
  extractUtmTagsFromOrder,
  validateUtmConfig,
  isUtmTaggingEnabled,
  getOrderWithUtmData
} from "../utils/utm";

// 🦠 BACTERIAL UTILITIES - Small, focused helper functions
const logger = {
  info: (jobId: string, message: string) => logToFile(jobId, "info", message),
  error: (jobId: string, message: string) => logToFile(jobId, "error", message),
  warn: (jobId: string, message: string) => logToFile(jobId, "warn", message),
};

// 🦠 BACTERIAL FUNCTION - Validate UTM job data
const validateUtmJobData = (jobData: any): { isValid: boolean; error?: string; data?: any } => {
  if (!jobData.orderId || !jobData.config) {
    return { isValid: false, error: "Missing orderId or config in job data" };
  }

  if (typeof jobData.orderId !== 'string' || !jobData.orderId.trim()) {
    return { isValid: false, error: "orderId must be a non-empty string" };
  }

  const configValidation = validateUtmConfig(jobData.config);
  if (!configValidation.isValid) {
    return { isValid: false, error: configValidation.error };
  }

  if (!isUtmTaggingEnabled(jobData.config)) {
    return { isValid: false, error: "No UTM tagging fields are enabled in config" };
  }

  return { isValid: true, data: jobData };
};

export async function handleOrderUtmTaggingJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = getJobId(job);

  try {
    logger.info(jobId, "Starting UTM tagging job");

    // 🦠 BACTERIAL APPROACH - Parse and validate job data
    const parseResult = parseJobData(job.data as string);
    if (!parseResult.isValid) {
      logger.error(jobId, `Invalid job data: ${parseResult.error}`);
      return false;
    }

    const validationResult = validateUtmJobData(parseResult.data);
    if (!validationResult.isValid) {
      logger.error(jobId, `Job data validation failed: ${validationResult.error}`);
      return false;
    }

    const { orderId, config } = validationResult.data!;

    logger.info(jobId, `Processing UTM tagging for order: ${orderId}`);

    // 🦠 BACTERIAL APPROACH - Fetch order data with UTM information
    const orderData = await getOrderWithUtmData(admin, orderId);
    if (!orderData) {
      logger.error(jobId, `Order ${orderId} not found`);
      return false;
    }

    // 🦠 BACTERIAL APPROACH - Extract UTM tags using focused utility
    const utmTags = extractUtmTagsFromOrder(orderData, config);

    if (utmTags.length === 0) {
      logger.info(jobId, `No new UTM tags to add for order ${orderId}`);
      return true;
    }

    // 🦠 BACTERIAL APPROACH - Apply tags using focused utility
    logger.info(jobId, `Adding UTM tags to order ${orderId}: [${utmTags.join(', ')}]`);

    const tagResult = await addOrderTags(admin, orderId, utmTags);

    if (!tagResult.success) {
      logger.error(jobId, `Failed to add UTM tags to order ${orderId}: ${JSON.stringify(tagResult.errors)}`);
      return false;
    }

    logger.info(jobId, `Successfully added UTM tags to order ${orderId}`);
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(jobId, `UTM tagging job failed: ${errorMessage}`);
    return false;
  }
}


