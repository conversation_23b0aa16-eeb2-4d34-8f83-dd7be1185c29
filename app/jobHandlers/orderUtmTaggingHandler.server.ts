import { logToFile } from "../utils/logger.server";
import { updateJobStatus } from "../queues/jobQueue.server";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";
import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";

// Define the structure of the automation configuration
interface AutomationConfig {
  tag_with_utm_campaign: boolean;
  tag_with_utm_source: boolean;
  tag_with_utm_medium: boolean;
  tag_with_utm_content: boolean;
  tag_with_utm_term: boolean;
}

// Define the structure of UTM parameters from Shopify
interface UtmParameters {
  campaign?: string;
  source?: string;
  medium?: string;
  content?: string;
  term?: string;
}

/**
 * Handles the job for tagging orders based on UTM parameters.
 * @param job The job to process.
 * @param admin The Shopify Admin API client.
 * @returns Promise<boolean> indicating if the job was processed successfully.
 */
export async function handleOrderUtmTaggingJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  try {
    logToFile(job.id, "info", "Starting processing of UTM tagging job");

    const jobData = JSON.parse(job.data);
    const orderData = jobData.order; // The full order object is now in the job data
    const config: AutomationConfig = jobData.config;

    if (!orderData || !config) {
      logToFile(job.id, "error", "No orderData or config found in job data");
      await updateJobStatus(job.id, "failed", "Missing orderData or config in job data");
      return false;
    }
    logToFile(job.id, "info", `Processing UTM tagging for order ID: ${orderData.id}`);

    // Process the order tagging
    const success = await processOrderUtmTagging(job.shop, orderData, config, admin, job.id);

    if (success) {
      logToFile(job.id, "info", `Successfully processed UTM tagging for order ${orderData.id}`);
      await updateJobStatus(job.id, "completed");
    } else {
      logToFile(job.id, "error", `Failed to process UTM tagging for order ${orderData.id}`);
      await updateJobStatus(job.id, "failed", "Failed to process UTM tagging for order");
    }
    return success;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(job.id, "error", `Processing failed for job ${job.id}: ${errorMessage}`);
    await updateJobStatus(job.id, "failed", errorMessage);
    return false;
  }
}

/**
 * Processes UTM tagging for a specific order.
 * @param shop The shop identifier.
 * @param orderId The Shopify order GID.
 * @param config The automation configuration.
 * @param admin The Shopify Admin API client.
 * @param jobId The job ID for logging.
 * @returns Promise<boolean> indicating if the tagging was successful.
 */
async function processOrderUtmTagging(shop: string, orderData: any, config: AutomationConfig, admin: ShopifyUnAuthenticatedAdminClient, jobId: string): Promise<boolean> {
  let orderId: string = ''; // Initialize orderId to an empty string
  try {
    orderId = orderData.id;
    const currentTags: string[] = orderData.tags || [];
    const moments = orderData.customerJourneySummary?.moments?.edges || [];
    const tagsToAddSet = new Set<string>();

    // Iterate through moments to extract UTM parameters
    for (const edge of moments) {
      const utmParameters: UtmParameters = edge.node.utmParameters || {};

      if (config.tag_with_utm_campaign && utmParameters.campaign && !currentTags.includes(utmParameters.campaign)) {
        tagsToAddSet.add(utmParameters.campaign);
      }
      if (config.tag_with_utm_source && utmParameters.source && !currentTags.includes(utmParameters.source)) {
        tagsToAddSet.add(utmParameters.source);
      }
      if (config.tag_with_utm_medium && utmParameters.medium && !currentTags.includes(utmParameters.medium)) {
        tagsToAddSet.add(utmParameters.medium);
      }
      if (config.tag_with_utm_content && utmParameters.content && !currentTags.includes(utmParameters.content)) {
        tagsToAddSet.add(utmParameters.content);
      }
      if (config.tag_with_utm_term && utmParameters.term && !currentTags.includes(utmParameters.term)) {
        tagsToAddSet.add(utmParameters.term);
      }
    }

    const uniqueTagsToAdd = Array.from(tagsToAddSet);

    // If there are tags to add, use Shopify API to add them
    if (uniqueTagsToAdd.length > 0) {
      const mutation = `#graphql
        mutation tagsAdd($id: ID!, $tags: [String!]!) {
          tagsAdd(id: $id, tags: $tags) {
            node { id }
            userErrors { field message }
          }
        }
      `;

      const mutationResponse = await shopifyGraphqlRequest(admin, mutation, {
        id: orderId,
        tags: uniqueTagsToAdd,
      }, jobId);

      const userErrors = mutationResponse.data?.tagsAdd?.userErrors || [];
      if (userErrors.length > 0) {
        logToFile(jobId, "error", `Failed to add tags to order ${orderId}: ${JSON.stringify(userErrors)}`);
        return false;
      }

      logToFile(jobId, "info", `Successfully added tags [${uniqueTagsToAdd.join(', ')}] to order ${orderId}`);
    } else {
      logToFile(jobId, "info", `No new UTM tags to add for order ${orderId}`);
    }

    return true;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(jobId, "error", `Error processing UTM tagging for order ${orderId}: ${errorMessage}`);
    return false;
  }
}
