import { logToFile } from "../utils/logger.server";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import { parseJobData, getJobId } from "../utils/job-data";
import {
  getInventoryItemData,
  validateInventoryItemData,
  isProductInStock,
  extractPublishedPublicationIds,
  createInventoryContext
} from "../utils/inventory";
import {
  fetchAllProductCollections,
  shouldCollectionBePublished,
  processCollectionPublication
} from "../utils/collection-publishing";

// 🦠 BACTERIAL UTILITIES - Small, focused helper functions
const logger = {
  info: (jobId: string, message: string) => logToFile(jobId, "info", message),
  error: (jobId: string, message: string) => logToFile(jobId, "error", message),
  warn: (jobId: string, message: string) => logToFile(jobId, "warn", message),
};

// 🦠 BACTERIAL FUNCTION - Validate inventory job data
const validateInventoryJobData = (jobData: any): { isValid: boolean; error?: string; inventoryItemId?: string } => {
  if (!jobData.inventoryItemId) {
    return { isValid: false, error: "Missing inventory_item_id in job data" };
  }

  return { isValid: true, inventoryItemId: jobData.inventoryItemId };
};

export async function handleInventoryUpdateJob(job: any, admin: any): Promise<boolean> {
  const jobId = getJobId(job);

  try {
    logger.info(jobId, "Starting inventory update job");

    // 🦠 BACTERIAL APPROACH - Parse and validate job data
    const parseResult = parseJobData(job.data);
    if (!parseResult.isValid) {
      logger.error(jobId, `Invalid job data: ${parseResult.error}`);
      return false;
    }

    const validationResult = validateInventoryJobData(parseResult.data);
    if (!validationResult.isValid) {
      logger.error(jobId, validationResult.error!);
      return false;
    }

    const inventoryItemId = validationResult.inventoryItemId!;
    logger.info(jobId, `Processing inventory update for item ID: ${inventoryItemId}`);

    // 🦠 BACTERIAL APPROACH - Get inventory item data using focused utility
    const inventoryItemData = await getInventoryItemData(admin, inventoryItemId);

    const validation = validateInventoryItemData(inventoryItemData, inventoryItemId);
    if (!validation.isValid) {
      logger.error(jobId, validation.reason!);
      return false;
    }

    const { variant } = inventoryItemData!;
    const product = variant.product;
    const inventoryQuantity = variant.inventoryQuantity || 0;

    // 🦠 BACTERIAL APPROACH - Use focused utility functions
    const productInStock = isProductInStock(inventoryQuantity);
    const productPublicationIds = extractPublishedPublicationIds(product);

    const context = createInventoryContext(inventoryItemId, product, inventoryQuantity);
    logger.info(jobId, `Inventory context: ${JSON.stringify(context)}`);

    // 🦠 BACTERIAL APPROACH - Fetch all collections using focused utility
    const allCollections = await fetchAllProductCollections(admin, product.id, product.collections);
    logger.info(jobId, `Found ${allCollections.length} collections for product`);

    // 🦠 BACTERIAL APPROACH - Process each collection using focused utilities
    for (const collection of allCollections) {
      logger.info(jobId, `🔄 About to determine if collection should be published: ${collection.title}`);

      // Determine if collection should be published
      const shouldPublish = await shouldCollectionBePublished(
        admin,
        productInStock,
        collection.id,
        productPublicationIds,
        product.id
      );

      logger.info(jobId, `🔄 About to call processCollectionPublication for: ${collection.title}, shouldPublish: ${shouldPublish}`);

      // Process collection publication
      await processCollectionPublication(
        admin,
        collection,
        shouldPublish,
        productPublicationIds,
        logger,
        jobId
      );

      logger.info(jobId, `🔄 Finished processCollectionPublication for: ${collection.title}`);
    }

    logger.info(jobId, "Inventory update job completed successfully");
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(jobId, `Inventory update job failed: ${errorMessage}`);
    return false;
  }
}
