import { logToFile } from "../utils/logger.server";
import { updateJobStatus } from "../queues/jobQueue.server";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";

export async function handleInventoryUpdateJob(job: any, admin: any): Promise<boolean> {
  try {
    logToFile(job.id, "info", "Starting processing of inventory update job");

    const jobData = JSON.parse(job.data);
    const inventoryItemId = jobData.inventoryItemId;
    if (!inventoryItemId) {
      logToFile(job.id, "error", "No inventory_item_id found in job data");
      await updateJobStatus(job.id, "failed", "Missing inventory_item_id in job data");
      return false;
    }
    logToFile(job.id, "info", `Processing inventory update for item ID: ${inventoryItemId}`);

    // The 'admin' client is already authenticated and ready to use.
    // No need for any authentication logic within this function.

    // Query to get product details including inventory and the first page of associated collections
    const INVENTORY_ITEM_QUERY = `#graphql
      query inventoryItemProductAndCollections($id: ID!) {
        inventoryItem(id: $id) {
          id
          variant {
            id
            inventoryQuantity
            product {
              id
              status
              resourcePublications(first: 250) {
                edges {
                  node {
                    publication {
                      id
                      name
                    }
                    isPublished
                  }
                }
              }
              collections(first: 250) {
                edges {
                  node {
                    id
                    title
                    resourcePublications(first: 250) {
                      edges {
                        node {
                          publication {
                            id
                            name
                          }
                          isPublished
                        }
                      }
                    }
                  }
                }
                pageInfo {
                  hasNextPage
                  endCursor
                }
              }
            }
          }
        }
      }`;

    const inventoryItemResponse = await shopifyGraphqlRequest(
      admin,
      INVENTORY_ITEM_QUERY,
      { id: `gid://shopify/InventoryItem/${inventoryItemId}` },
      job.id
    );

    const inventoryItem = inventoryItemResponse.data?.inventoryItem;

    if (!inventoryItem || !inventoryItem.variant || !inventoryItem.variant.product) {
      logToFile(job.id, "error", `Inventory item with ID ${inventoryItemId} or its associated product/variant not found.`);
      await updateJobStatus(job.id, "failed", `Inventory item with ID ${inventoryItemId} or its associated product/variant not found.`);
      return false;
    }

    const product = inventoryItem.variant.product;
    const inventoryQuantity = inventoryItem.variant?.inventoryQuantity || 0;

    const isProductInStock = inventoryQuantity > 0;
    const isProductPublished = product.status === "ACTIVE" && product.resourcePublications.edges.some(
      (edge: any) => edge.node.isPublished
    );

    logToFile(job.id, "info", `Product ${product.id} (from inventory item ${inventoryItemId}) - In Stock: ${isProductInStock}, Published: ${isProductPublished}`);

    // --- OPTIMIZATION START ---
    // Use the collection data from the initial query as the first page.
    const initialCollectionsData = product.collections;
    let allCollections: any[] = initialCollectionsData.edges.map((edge: any) => edge.node);
    let hasNextPage = initialCollectionsData.pageInfo?.hasNextPage || false;
    let cursor: string | null = initialCollectionsData.pageInfo?.endCursor || null;

    // This loop now correctly starts from the second page if one exists,
    // avoiding the redundant fetch of the first page of collections.
    while (hasNextPage) {
      const collectionsResponse = await shopifyGraphqlRequest(
        admin,
        `#graphql
          query productCollections($id: ID!, $first: Int!, $after: String) {
            product(id: $id) {
              collections(first: $first, after: $after) {
                edges {
                  node {
                    id
                    title
                    resourcePublications(first: 250) {
                      edges {
                        node {
                          publication {
                            id
                            name
                          }
                          isPublished
                        }
                      }
                    }
                  }
                }
                pageInfo {
                  hasNextPage
                  endCursor
                }
              }
            }
          }`,
        { id: product.id, first: 250, after: cursor },
        job.id
      );

      const collectionsData = collectionsResponse.data?.product?.collections;
      if (collectionsData?.edges) {
        allCollections = allCollections.concat(collectionsData.edges.map((edge: any) => edge.node));
      }
      hasNextPage = collectionsData?.pageInfo?.hasNextPage || false;
      cursor = collectionsData?.pageInfo?.endCursor || null;
    }
    // --- OPTIMIZATION END ---

    for (const collection of allCollections) {
      logToFile(job.id, "info", `Processing collection: ${collection.title} (ID: ${collection.id})`);

      let shouldCollectionBePublished = true; // Default to true, assuming collection should be published
      if (!isProductInStock) {
        // If the current product is out of stock, check if other products in the collection are in stock and published
        shouldCollectionBePublished = await checkOtherProductsInCollection(
          admin,
          collection.id,
          job.id,
          product.resourcePublications.edges.map((edge: any) => edge.node.publication.id),
          product.id // Pass the current product ID to exclude it from the check
        );
      }

      logToFile(job.id, "info", `Collection ${collection.title} - Should be published: ${shouldCollectionBePublished}`);

      for (const pubEdge of collection.resourcePublications.edges) {
        const publicationNode = pubEdge.node;
        const isCollectionPublishedInChannel = publicationNode.isPublished;
        const publicationId = publicationNode.publication.id;
        const publicationName = publicationNode.publication.name;

        if (shouldCollectionBePublished) {
          // Logic to PUBLISH collection
          if (!isCollectionPublishedInChannel) { // If collection is not already published here
            // Check if the product that triggered this is published in this channel
            const isProductPublishedInThisChannel = product.resourcePublications.edges.some(
              (pEdge: any) => pEdge.node.publication.id === publicationId && pEdge.node.isPublished
            );
            if (isProductPublishedInThisChannel) {
              logToFile(job.id, "info", `Publishing collection ${collection.title} to ${publicationName}`);
              const PUBLISH_COLLECTION_MUTATION = `#graphql
                mutation PublishablePublish($id: ID!, $publicationId: ID!) {
                  publishablePublish(id: $id, input: {publicationId: $publicationId}) {
                    publishable {
                      publishedOnPublication(publicationId: $publicationId)
                    }
                    userErrors {
                      field
                      message
                    }
                  }
                }`;
              await shopifyGraphqlRequest(
                admin,
                PUBLISH_COLLECTION_MUTATION,
                { id: collection.id, publicationId: publicationId },
                job.id
              );
            } else {
              logToFile(job.id, "info", `Skipping publishing collection ${collection.title} to ${publicationName} as the triggering product is not published there.`);
            }
          }
        } else {
          // Logic to UNPUBLISH collection
          if (isCollectionPublishedInChannel) { // If collection is currently published here
            logToFile(job.id, "info", `Unpublishing collection ${collection.title} from ${publicationName}`);
            const UNPUBLISH_COLLECTION_MUTATION = `#graphql
              mutation PublishableUnpublish($id: ID!, $publicationId: ID!) {
                publishableUnpublish(id: $id, input: {publicationId: $publicationId}) {
                  publishable {
                    publishedOnPublication(publicationId: $publicationId)
                  }
                  userErrors {
                    field
                    message
                  }
                }
              }`;
            await shopifyGraphqlRequest(
              admin,
              UNPUBLISH_COLLECTION_MUTATION,
              { id: collection.id, publicationId: publicationId },
              job.id
            );
          }
        }
      }
    }

    return true;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(job.id, "error", `Processing failed for job ${job.id}: ${errorMessage}`);
    await updateJobStatus(job.id, "failed", errorMessage);
    return false;
  }
}

async function checkOtherProductsInCollection(
  admin: any,
  collectionId: string,
  jobId: string,
  productPublicationIds: string[],
  currentProductId: string // New parameter to exclude the current product
): Promise<boolean> {
  let hasOtherPublishedInStockProducts = false;
  let cursor: string | null = null;

  while (true) {
    const COLLECTION_PRODUCTS_QUERY = `#graphql
      query collectionProducts($id: ID!, $first: Int!, $after: String) {
        collection(id: $id) {
          products(first: $first, after: $after) {
            edges {
              node {
                id
                status
                resourcePublications(first: 250) {
                  edges {
                    node {
                      publication {
                        id
                      }
                      isPublished
                    }
                  }
                }
                variants(first: 250) {
                  edges {
                    node {
                      inventoryQuantity
                    }
                  }
                }
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      }`;

    const productsResponse = await shopifyGraphqlRequest(
      admin,
      COLLECTION_PRODUCTS_QUERY,
      { id: collectionId, first: 250, after: cursor },
      jobId
    );

    const productsInCollection = productsResponse.data?.collection?.products?.edges || [];

    for (const productEdge of productsInCollection) {
      const product = productEdge.node;

      // Exclude the current product from the check
      if (product.id === currentProductId) {
        continue;
      }

      const isProductInStock = product.variants.edges.some(
        (edge: any) => edge.node.inventoryQuantity > 0
      );
      const isProductPublishedInRelevantChannel = product.status === "ACTIVE" && product.resourcePublications.edges.some(
        (pubEdge: any) => productPublicationIds.includes(pubEdge.node.publication.id) && pubEdge.node.isPublished
      );

      if (isProductInStock && isProductPublishedInRelevantChannel) {
        hasOtherPublishedInStockProducts = true;
        break;
      }
    }

    if (!productsResponse.data?.collection?.products?.pageInfo?.hasNextPage || hasOtherPublishedInStockProducts) {
      break;
    }
    cursor = productsResponse.data?.collection?.products?.pageInfo?.endCursor;
  }

  return hasOtherPublishedInStockProducts;
}
