import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import { createEnumValidator } from "../utils/validation";
import { parseJobData, getJobId, createJobR<PERSON>ult, logJobStep } from "../utils/job-data";
import {
  getOrderForCancellation,
  cancelOrder,
  addOrderTags,
  isOrderCancelled,
  isOrderPaid,
  isOrderFulfilled,
  hasHighRisk,
  canCancelOrder,
  ORDER_CANCELLATION_REASONS,
  type OrderCancellationReason
} from "../utils/shopify-orders";

interface CancelHighRiskOrderConfig {
  cancellation_reason_to_set: OrderCancellationReason;
  ignore_unpaid_orders: boolean;
  refund_payment_for_cancelled_orders: boolean;
  restock_inventory_for_cancelled_orders: boolean;
  email_customer_when_cancelling: boolean;
  staff_note_for_timeline: string;
  add_this_order_tag_when_cancelling: string;
}

interface JobData {
  orderId: string;
  config: CancelHighRiskOrderConfig;
}

// 🦠 BACTERIAL UTILITIES - Small, focused helper functions
const validateCancellationReason = createEnumValidator(ORDER_CANCELLATION_REASONS);

const logger = {
  info: (jobId: string, message: string) => logToFile(jobId, "info", message),
  error: (jobId: string, message: string) => logToFile(jobId, "error", message),
  warn: (jobId: string, message: string) => logToFile(jobId, "warn", message),
};

// 🦠 BACTERIAL FUNCTION - Validate job data
const validateJobData = (jobData: any): { isValid: boolean; error?: string; data?: JobData } => {
  if (!jobData.orderId || !jobData.config) {
    return { isValid: false, error: "Missing orderId or config" };
  }

  const validReason = validateCancellationReason(jobData.config.cancellation_reason_to_set);
  if (!validReason) {
    return {
      isValid: false,
      error: `Invalid cancellation reason: '${jobData.config.cancellation_reason_to_set}'. Must be one of ${ORDER_CANCELLATION_REASONS.join(", ")}.`
    };
  }

  return {
    isValid: true,
    data: {
      orderId: jobData.orderId,
      config: { ...jobData.config, cancellation_reason_to_set: validReason }
    }
  };
};

// 🦠 BACTERIAL FUNCTION - Check if order should be processed
const shouldProcessOrder = (order: any, config: CancelHighRiskOrderConfig): { shouldProcess: boolean; reason?: string } => {
  if (isOrderCancelled(order)) {
    return { shouldProcess: false, reason: `Order ${order.name} (${order.id}) has already been cancelled` };
  }

  if (config.ignore_unpaid_orders && !isOrderPaid(order)) {
    return { shouldProcess: false, reason: `Order ${order.name} (${order.id}) is not paid and 'ignore unpaid' is enabled` };
  }

  const cancelCheck = canCancelOrder(order);
  if (!cancelCheck.canCancel) {
    return { shouldProcess: false, reason: `Order ${order.name} (${order.id}) ${cancelCheck.reason}` };
  }

  if (!hasHighRisk(order)) {
    return { shouldProcess: false, reason: `Order ${order.name} (${order.id}) does not have a HIGH risk level` };
  }

  return { shouldProcess: true };
};

export async function handleCancelHighRiskOrderJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = getJobId(job);

  try {
    logJobStep(logger, jobId, 'info', 'Starting cancel high-risk order job');

    // 🦠 BACTERIAL APPROACH - Parse and validate job data
    const parseResult = parseJobData<JobData>(job.data as string, { orderId: 'string', config: 'object' });
    if (!parseResult.isValid) {
      logJobStep(logger, jobId, 'error', 'Invalid job data', { error: parseResult.error });
      return false;
    }

    const validationResult = validateJobData(parseResult.data);
    if (!validationResult.isValid) {
      logJobStep(logger, jobId, 'error', 'Job data validation failed', { error: validationResult.error });
      return false;
    }

    const { orderId, config } = validationResult.data!;

    // 🦠 BACTERIAL APPROACH - Get order using focused utility
    const order = await getOrderForCancellation(admin, orderId);

    if (!order) {
      logJobStep(logger, jobId, 'warn', 'Order not found, skipping', { orderId });
      return true;
    }

    // 🦠 BACTERIAL APPROACH - Use focused validation function
    const processCheck = shouldProcessOrder(order, config);
    if (!processCheck.shouldProcess) {
      logJobStep(logger, jobId, 'info', processCheck.reason!);
      return true;
    }

    logJobStep(logger, jobId, 'info', 'Proceeding to cancel high-risk order', {
      orderName: order.name,
      orderId: order.id,
      config
    });

    // 🦠 BACTERIAL APPROACH - Use focused cancellation utility
    const cancelResult = await cancelOrder(admin, order.id, {
      reason: config.cancellation_reason_to_set,
      notifyCustomer: config.email_customer_when_cancelling,
      refund: config.refund_payment_for_cancelled_orders,
      restock: config.restock_inventory_for_cancelled_orders,
      staffNote: config.staff_note_for_timeline,
    });

    if (!cancelResult.success) {
      logJobStep(logger, jobId, 'error', 'Failed to cancel order', {
        orderId,
        errors: cancelResult.errors
      });
      return false;
    }

    logJobStep(logger, jobId, 'info', 'Successfully cancelled order', { orderId });

    // 🦠 BACTERIAL APPROACH - Use focused tagging utility
    if (config.add_this_order_tag_when_cancelling?.trim()) {
      const tagToAdd = config.add_this_order_tag_when_cancelling.trim();
      logJobStep(logger, jobId, 'info', 'Adding tag to cancelled order', {
        orderId,
        tag: tagToAdd
      });

      const tagResult = await addOrderTags(admin, order.id, [tagToAdd]);

      if (!tagResult.success) {
        logJobStep(logger, jobId, 'warn', 'Order was cancelled, but failed to add tag', {
          orderId,
          errors: tagResult.errors
        });
      } else {
        logJobStep(logger, jobId, 'info', 'Successfully tagged order', { orderId });
      }
    }

    logJobStep(logger, jobId, 'info', 'Cancel high-risk order job completed successfully');
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logJobStep(logger, jobId, 'error', 'Unexpected error occurred', { error: errorMessage });
    return false;
  }
}