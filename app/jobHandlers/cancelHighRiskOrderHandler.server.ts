import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";

interface CancelHighRiskOrderConfig {
  cancellation_reason_to_set: "customer" | "declined" | "fraud" | "inventory" | "other" | "staff";
  ignore_unpaid_orders: boolean;
  refund_payment_for_cancelled_orders: boolean;
  restock_inventory_for_cancelled_orders: boolean;
  email_customer_when_cancelling: boolean;
  staff_note_for_timeline: string;
  add_this_order_tag_when_cancelling: string;
}

interface JobData {
  orderId: string;
  config: CancelHighRiskOrderConfig;
}

export async function handleCancelHighRiskOrderJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = job.id;
  try {
    logToFile(jobId, "info", "Starting processing of cancel high-risk order job.");
    const jobData: JobData = JSON.parse(job.data as string);
    const { orderId, config } = jobData;

    if (!orderId || !config) {
      logToFile(jobId, "error", "Invalid job data: Missing orderId or config.");
      return false;
    }

    const validCancellationReasons = ["customer", "declined", "fraud", "inventory", "other", "staff"];
    const cancellationReason = config.cancellation_reason_to_set.toLowerCase();
    if (!validCancellationReasons.includes(cancellationReason)) {
      logToFile(jobId, "error", `Invalid cancellation reason: '${cancellationReason}'. Must be one of ${validCancellationReasons.join(", ")}.`);
      return false;
    }

    const GET_ORDER_QUERY = `#graphql
      query getOrderForCancellation($orderId: ID!) {
        order(id: $orderId) {
          id
          name
          cancelledAt
          displayFinancialStatus
          displayFulfillmentStatus
          risk {
            assessments {
              riskLevel
            }
          }
        }
      }`;

    const orderResponse = await shopifyGraphqlRequest(admin, GET_ORDER_QUERY, { orderId }, jobId);
    const order = orderResponse.data?.order;

    if (!order) {
      logToFile(jobId, "warn", `Order ${orderId} not found. Skipping.`);
      return true;
    }

    if (order.cancelledAt) {
      logToFile(jobId, "info", `Order ${order.name} (${orderId}) has already been cancelled. Job complete.`);
      return true;
    }

    if (config.ignore_unpaid_orders && order.displayFinancialStatus !== "PAID") {
      logToFile(jobId, "info", `Order ${order.name} (${orderId}) is not paid and 'ignore unpaid' is enabled. Skipping.`);
      return true;
    }

    if (order.displayFulfillmentStatus === "FULFILLED" || order.displayFulfillmentStatus === "PARTIALLY_FULFILLED") {
      logToFile(jobId, "warn", `Order ${order.name} (${orderId}) is fulfilled or partially fulfilled and cannot be cancelled. Job complete.`);
      return true;
    }

    // logToFile(jobId, "warn", "HIGH-RISK CHECK IS CURRENTLY BYPASSED FOR TESTING!");

    const hasHighRisk = order.risk?.assessments?.some((assessment: any) => assessment.riskLevel === "HIGH");
    if (!hasHighRisk) {
      logToFile(jobId, "info", `Order ${order.name} (${orderId}) does not have a HIGH risk level. Skipping.`);
      return true;
    }

    logToFile(jobId, "info", `Proceeding to cancel high-risk order ${order.name} (${orderId}).`);
    logToFile(jobId, "info", `configs: ${JSON.stringify(config)}`);

    const CANCEL_ORDER_MUTATION = `#graphql
      mutation OrderCancel($orderId: ID!, $notifyCustomer: Boolean, $reason: OrderCancelReason!, $refund: Boolean!, $restock: Boolean!, $staffNote: String) {
        orderCancel(orderId: $orderId, notifyCustomer: $notifyCustomer, refund: $refund, restock: $restock, reason: $reason, staffNote: $staffNote) {
          job {
            id
          }
          userErrors {
            field
            message
          }
        }
      }`;

    const cancelResponse = await shopifyGraphqlRequest(admin, CANCEL_ORDER_MUTATION, {
      orderId: order.id,
      notifyCustomer: config.email_customer_when_cancelling,
      reason: cancellationReason.toUpperCase(),
      refund: config.refund_payment_for_cancelled_orders,
      restock: config.restock_inventory_for_cancelled_orders,
      staffNote: config.staff_note_for_timeline,
    }, jobId);

    const cancelErrors = cancelResponse.data?.orderCancel?.userErrors;
    if (cancelErrors && cancelErrors.length > 0) {
      logToFile(jobId, "error", `Failed to cancel order ${orderId}: ${JSON.stringify(cancelErrors)}`);
      return false;
    }

    logToFile(jobId, "info", `Successfully cancelled order ${orderId}.`);

    if (config.add_this_order_tag_when_cancelling && config.add_this_order_tag_when_cancelling.trim() !== "") {
      logToFile(jobId, "info", `Adding tag "${config.add_this_order_tag_when_cancelling}" to order ${orderId}.`);
      const ADD_TAG_MUTATION = `#graphql
        mutation tagsAdd($id: ID!, $tags: [String!]!) {
          tagsAdd(id: $id, tags: $tags) {
            userErrors {
              field
              message
            }
          }
        }`;
      const tagResponse = await shopifyGraphqlRequest(admin, ADD_TAG_MUTATION, {
        id: order.id,
        tags: [config.add_this_order_tag_when_cancelling.trim()],
      }, jobId);

      const tagErrors = tagResponse.data?.tagsAdd?.userErrors;
      if (tagErrors && tagErrors.length > 0) {
        logToFile(jobId, "warn", `Order was cancelled, but failed to add tag: ${JSON.stringify(tagErrors)}`);
      } else {
        logToFile(jobId, "info", `Successfully tagged order ${orderId}.`);
      }
    }

    logToFile(jobId, "info", "Cancel high-risk order job completed successfully.");
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(jobId, "error", `An unexpected error occurred: ${errorMessage}`);
    return false;
  }
}