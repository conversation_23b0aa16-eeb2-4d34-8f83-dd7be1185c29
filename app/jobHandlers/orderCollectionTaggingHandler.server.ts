import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";

// --- Type Definitions for Job Data ---

interface CollectionTagPair {
  key: string;
  value: string;
}

interface OrderCollectionTagConfig {
  collections_and_tags: CollectionTagPair[];
}

interface JobData {
  orderId: string; // Shopify Order GID, e.g., "gid://shopify/Order/1234567890"
  productIds?: string[]; // Optional: Enriched data from the bulk handler
  config: OrderCollectionTagConfig;
}

/**
 * Fetches product IDs and existing tags for an order.
 * If productIds are provided in the job data, it uses them.
 * Otherwise, it fetches them via a GraphQL query.
 */
async function getOrderDetails(
  orderId: string,
  jobData: JobData,
  admin: ShopifyUnAuthenticatedAdminClient,
  jobId: string,
): Promise<{ existingTags: Set<string>; orderProductIds: string[] } | null> {
  if (jobData.productIds) {
    logToFile(jobId, "info", "Using enriched productIds from job data.");
    // Need to fetch tags separately as they aren't in the bulk op data
    const getOrderTagsQuery = `#graphql
      query getOrderTags($orderId: ID!) {
        order(id: $orderId) {
          tags
        }
      }`;
    const response = await shopifyGraphqlRequest(admin, getOrderTagsQuery, { orderId }, jobId);
    const order = response.data?.order;
    return {
      existingTags: new Set(order?.tags || []),
      orderProductIds: jobData.productIds,
    };
  }

  logToFile(jobId, "info", "Fetching order details via GraphQL.");
  const getOrderDetailsQuery = `#graphql
    query getOrderDetails($orderId: ID!) {
      order(id: $orderId) {
        tags
        lineItems(first: 250) {
          nodes {
            product {
              id
            }
          }
        }
      }
    }`;

  const response = await shopifyGraphqlRequest(admin, getOrderDetailsQuery, { orderId }, jobId);
  const order = response.data?.order;

  if (!order) {
    logToFile(jobId, "warn", `Order ${orderId} not found. Skipping.`);
    return null;
  }

  const existingTags = new Set<string>(order.tags || []);
  const orderProductIds: string[] = [
    ...new Set<string>(
      order.lineItems.nodes
        .map((item: any): string | undefined => item.product?.id)
        .filter((id: string | undefined): id is string => Boolean(id)),
    ),
  ];

  return { existingTags, orderProductIds };
}


/**
 * Handles the job for tagging an order based on the collections of its products.
 *
 * @param job The job object from the database.
 * @param admin The authenticated Shopify Admin API client.
 * @returns A promise that resolves to true on success, false on failure.
 */
export async function handleOrderCollectionTaggingJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = job.id;
  try {
    // 1. --- PARSE AND VALIDATE JOB DATA ---
    logToFile(jobId, "info", "Starting processing of order collection tagging job.");
    const jobData: JobData = JSON.parse(job.data as string);
    const { orderId, config } = jobData;

    if (!orderId || !config?.collections_and_tags) {
      logToFile(jobId, "error", "Invalid job data: Missing orderId or config.");
      return false;
    }
    logToFile(jobId, "info", `Processing order ${orderId}.`);

    // 2. --- GET PRODUCT IDS & EXISTING TAGS FROM ORDER (ENRICHED) ---
    const details = await getOrderDetails(orderId, jobData, admin, jobId);
    if (!details) {
      return true; // Not a retryable error
    }
    const { existingTags, orderProductIds } = details;

    if (orderProductIds.length === 0) {
      logToFile(jobId, "info", `Order ${orderId} has no products. Nothing to tag. Job complete.`);
      return true;
    }
    logToFile(jobId, "info", `Order product IDs: ${JSON.stringify(orderProductIds)}`);


    // 3. --- FETCH COLLECTIONS FOR ALL PRODUCTS IN THE ORDER ---
    const search_query = orderProductIds.map(id => {
      const numericId = id.split('/').pop();
      return `product_id:${numericId}`;
    }).join(" OR ");
    logToFile(jobId, "info", `Constructed collection search query: ${search_query}`);

    let allCollections: any[] = [];
    let cursor: string | null = null;
    let hasNextPage = true;

    const getCollectionsQuery = `#graphql
      query getCollectionsForProducts($query: String!, $cursor: String) {
        collections(first: 250, query: $query, after: $cursor) {
          pageInfo { hasNextPage endCursor }
          nodes {
            id
            legacyResourceId
            title
            handle
          }
        }
      }`;

    while (hasNextPage) {
      const collectionsResponse = await shopifyGraphqlRequest(admin, getCollectionsQuery, { query: search_query, cursor }, jobId);
      const collectionsData = collectionsResponse.data?.collections;
      if (collectionsData?.nodes) {
        allCollections = allCollections.concat(collectionsData.nodes);
      }
      hasNextPage = collectionsData?.pageInfo?.hasNextPage || false;
      cursor = collectionsData?.pageInfo?.endCursor || null;
    }
    logToFile(jobId, "info", `Found ${allCollections.length} collections associated with the order's products.`);

    // 4. --- CREATE A SET OF ALL POSSIBLE COLLECTION IDENTIFIERS ---
    const collectionIdentifierSet = new Set<string>();
    allCollections.forEach(collection => {
      collectionIdentifierSet.add(collection.id);
      collectionIdentifierSet.add(collection.legacyResourceId);
      collectionIdentifierSet.add(collection.title.toLowerCase());
      collectionIdentifierSet.add(collection.handle.toLowerCase());
    });

    // 5. --- DETERMINE WHICH TAGS TO ADD ---
    const tagsToAddSet = new Set<string>();
    for (const pair of config.collections_and_tags) {
      if (collectionIdentifierSet.has(pair.key.toLowerCase())) {
        const tagsFromConfig = pair.value.split(',').map(tag => tag.trim()).filter(Boolean);
        for (const tag of tagsFromConfig) {
          if (!existingTags.has(tag)) {
            tagsToAddSet.add(tag);
          }
        }
      }
    }

    // 6. --- APPLY TAGS IF NECESSARY ---
    if (tagsToAddSet.size > 0) {
      const tagsToAdd = Array.from(tagsToAddSet);
      logToFile(jobId, "info", `Adding tags to order ${orderId}: [${tagsToAdd.join(', ')}]`);

      const addTagsMutation = `#graphql
        mutation tagsAdd($id: ID!, $tags: [String!]!) {
          tagsAdd(id: $id, tags: $tags) {
            node { id }
            userErrors { field message }
          }
        }`;

      const mutationResponse = await shopifyGraphqlRequest(admin, addTagsMutation, { id: orderId, tags: tagsToAdd }, jobId);
      const userErrors = mutationResponse.data?.tagsAdd?.userErrors;
      if (userErrors && userErrors.length > 0) {
        logToFile(jobId, "error", `Failed to add tags to order ${orderId}: ${JSON.stringify(userErrors)}`);
        return false; // Fail the job so it can be retried
      }

      logToFile(jobId, "info", `Successfully added tags to order ${orderId}.`);
    } else {
      logToFile(jobId, "info", `No new tags to add for order ${orderId}.`);
    }

    logToFile(jobId, "info", "Order collection tagging job completed successfully.");
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(jobId, "error", `An unexpected error occurred: ${errorMessage}`);
    return false; // Let the worker handle the failure.
  }
}
