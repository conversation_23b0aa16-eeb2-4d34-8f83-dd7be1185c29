import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import { parseJobData, getJobId } from "../utils/job-data";
import {
  processOrderDetails,
  validateOrderResult,
  logOrderStep
} from "../utils/order-processing";
import {
  getCollectionsForProducts,
  createCollectionIdentifierSet,
  determineTagsToAdd
} from "../utils/shopify-collections";
import { addOrderTags } from "../utils/shopify-orders";

// --- Type Definitions for Job Data ---

interface CollectionTagPair {
  key: string;
  value: string;
}

interface OrderCollectionTagConfig {
  collections_and_tags: CollectionTagPair[];
}

interface JobData {
  orderId: string; // Shopify Order GID, e.g., "gid://shopify/Order/1234567890"
  productIds?: string[]; // Optional: Enriched data from the bulk handler
  config: OrderCollectionTagConfig;
}

// 🦠 BACTERIAL UTILITIES - Small, focused helper functions
const logger = {
  info: (jobId: string, message: string) => logToFile(jobId, "info", message),
  error: (jobId: string, message: string) => logToFile(jobId, "error", message),
  warn: (jobId: string, message: string) => logToFile(jobId, "warn", message),
};

// 🦠 BACTERIAL FUNCTION - Validate job data structure
const validateJobData = (jobData: any): { isValid: boolean; error?: string; data?: JobData } => {
  if (!jobData.orderId || !jobData.config?.collections_and_tags) {
    return { isValid: false, error: "Missing orderId or config.collections_and_tags" };
  }

  return { isValid: true, data: jobData as JobData };
};


export async function handleOrderCollectionTaggingJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = getJobId(job);

  try {
    logOrderStep(logger, jobId, 'info', 'Starting order collection tagging job');

    // 🦠 BACTERIAL APPROACH - Parse and validate job data
    const parseResult = parseJobData<JobData>(job.data as string, { orderId: 'string', config: 'object' });
    if (!parseResult.isValid) {
      logOrderStep(logger, jobId, 'error', 'Invalid job data', { error: parseResult.error });
      return false;
    }

    const validationResult = validateJobData(parseResult.data);
    if (!validationResult.isValid) {
      logOrderStep(logger, jobId, 'error', 'Job data validation failed', { error: validationResult.error });
      return false;
    }

    const { orderId, config } = validationResult.data!;
    logOrderStep(logger, jobId, 'info', 'Processing order', { orderId });

    // 🦠 BACTERIAL APPROACH - Process order details using strategy pattern
    const orderResult = await processOrderDetails(admin, orderId, parseResult.data!, logger, jobId);
    const validation = validateOrderResult(orderResult, orderId);

    if (!validation.isValid) {
      logOrderStep(logger, jobId, 'info', validation.reason!);
      return true; // Not a retryable error
    }

    const { existingTags, productIds } = orderResult!;
    logOrderStep(logger, jobId, 'info', 'Order details processed', {
      productCount: productIds.length,
      existingTagCount: existingTags.size
    });

    // 🦠 BACTERIAL APPROACH - Get collections using focused utility
    const collections = await getCollectionsForProducts(
      admin,
      productIds,
      (count) => logOrderStep(logger, jobId, 'info', 'Collections fetched', { count })
    );

    logOrderStep(logger, jobId, 'info', 'Collections retrieved', {
      collectionCount: collections.length
    });

    // 🦠 BACTERIAL APPROACH - Create identifiers and determine tags
    const collectionIdentifiers = createCollectionIdentifierSet(collections);
    const tagsToAdd = determineTagsToAdd(
      collectionIdentifiers,
      config.collections_and_tags,
      existingTags
    );

    // 🦠 BACTERIAL APPROACH - Apply tags using focused utility
    if (tagsToAdd.length > 0) {
      logOrderStep(logger, jobId, 'info', 'Adding tags to order', {
        orderId,
        tags: tagsToAdd
      });

      const tagResult = await addOrderTags(admin, orderId, tagsToAdd);

      if (!tagResult.success) {
        logOrderStep(logger, jobId, 'error', 'Failed to add tags', {
          orderId,
          errors: tagResult.errors
        });
        return false;
      }

      logOrderStep(logger, jobId, 'info', 'Successfully added tags', { orderId });
    } else {
      logOrderStep(logger, jobId, 'info', 'No new tags to add', { orderId });
    }

    logOrderStep(logger, jobId, 'info', 'Order collection tagging job completed successfully');
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logOrderStep(logger, jobId, 'error', 'Unexpected error occurred', { error: errorMessage });
    return false;
  }
}
