import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";

// --- Type Definitions for Job Data ---

interface CartAttributeTagConfig {
  cart_attribute_to_monitor: string;
}

// The lean object extracted from the webhook payload
interface LeanOrderData {
  admin_graphql_api_id: string;
  tags: string; // Comes as a comma-separated string from the webhook
  note_attributes: { name: string; value: string }[];
}

// Job data can now contain the lean order object or just the ID for bulk runs
interface JobData {
  order?: LeanOrderData;
  orderId?: string; // Shopify Order GID, e.g., "gid://shopify/Order/1234567890"
  config: CartAttributeTagConfig;
}

/**
 * Fetches order details from Shopify ONLY if they are not already provided in the job data.
 */
async function getOrderDetails(jobData: JobData, admin: ShopifyUnAuthenticatedAdminClient, jobId: string) {
  // If the lean order object is in the job data (from the webhook), use it.
  if (jobData.order) {
    logToFile(jobId, "info", "Using lean order data from webhook payload.");
    // Normalize the lean data to match the GraphQL structure for consistent processing.
    return {
      id: jobData.order.admin_graphql_api_id,
      tags: jobData.order.tags.split(',').map((t: string) => t.trim()).filter(Boolean),
      customAttributes: jobData.order.note_attributes.map((attr) => ({
        key: attr.name,
        value: attr.value,
      })),
    };
  }

  return null;
}


/**
 * Handles the job for tagging an order based on a specific cart note attribute.
 *
 * @param job The job object from the database.
 * @param admin The authenticated Shopify Admin API client.
 * @returns A promise that resolves to true on success, false on failure.
 */
export async function handleOrderCartAttributeTaggingJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = job.id;
  try {
    // 1. --- PARSE AND VALIDATE JOB DATA ---
    logToFile(jobId, "info", "Starting processing of order cart attribute tagging job.");
    const jobData: JobData = JSON.parse(job.data as string);
    const { config } = jobData;
    const attributeNameToFind = config?.cart_attribute_to_monitor;

    if (!jobData.order && !jobData.orderId) {
      logToFile(jobId, "error", `Invalid job data: Missing order or orderId.`);
      return false;
    }
    if (!attributeNameToFind) {
      logToFile(jobId, "error", `Invalid job data: Missing cart_attribute_to_monitor. Config: ${JSON.stringify(config)}`);
      return false;
    }

    // 2. --- GET ORDER DETAILS (EFFICIENTLY) ---
    const order = await getOrderDetails(jobData, admin, jobId);

    if (!order) {
      logToFile(jobId, "warn", `Order not found or could not be retrieved. Skipping.`);
      return true; // Not a retryable error.
    }
    
    const orderId = order.id;
    logToFile(jobId, "info", `Processing order ${orderId} for attribute "${attributeNameToFind}".`);

    // 3. --- FIND THE ATTRIBUTE AND ITS VALUE ---
    const targetAttribute = order.customAttributes.find(
      (attr: { key: string; value: string }) => attr.key === attributeNameToFind
    );

    const attributeValue = targetAttribute?.value;

    if (!attributeValue) {
      logToFile(jobId, "info", `Attribute "${attributeNameToFind}" not found on order ${orderId} or its value is empty. Job complete.`);
      return true;
    }

    // 4. --- CHECK IF TAG ALREADY EXISTS ---
    const existingTags = order.tags || [];
    if (existingTags.includes(attributeValue)) {
      logToFile(jobId, "info", `Order ${orderId} is already tagged with "${attributeValue}". Job complete.`);
      return true;
    }

    // 5. --- APPLY THE TAG ---
    logToFile(jobId, "info", `Adding tag "${attributeValue}" to order ${orderId}.`);

    const addTagMutation = `#graphql
      mutation tagsAdd($id: ID!, $tags: [String!]!) {
        tagsAdd(id: $id, tags: $tags) {
          node { id }
          userErrors { field message }
        }
      }`;

    const mutationResponse = await shopifyGraphqlRequest(admin, addTagMutation, { id: orderId, tags: [attributeValue] }, jobId);
    const userErrors = mutationResponse.data?.tagsAdd?.userErrors;

    if (userErrors && userErrors.length > 0) {
      logToFile(jobId, "error", `Failed to add tag to order ${orderId}: ${JSON.stringify(userErrors)}`);
      return false; // Fail the job so it can be retried.
    }

    logToFile(jobId, "info", `Successfully added tag to order ${orderId}.`);
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(jobId, "error", `An unexpected error occurred: ${errorMessage}`);
    return false; // Let the worker handle the failure.
  }
}