import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import { parseJobData, getJobId } from "../utils/job-data";
import { addOrderTags } from "../utils/shopify-orders";
import {
  extractOrderData,
  processCartAttributeTagging,
  validateCartAttributeJobData,
  createCartAttributeContext,
  type CartAttributeTagConfig,
  type LeanOrderData
} from "../utils/cart-attributes";

// Job data can now contain the lean order object or just the ID for bulk runs
interface JobData {
  order?: LeanOrderData;
  orderId?: string; // Shopify Order GID, e.g., "gid://shopify/Order/1234567890"
  config: CartAttributeTagConfig;
}

// 🦠 BACTERIAL UTILITIES - Small, focused helper functions
const logger = {
  info: (jobId: string, message: string) => logToFile(jobId, "info", message),
  error: (jobId: string, message: string) => logToFile(jobId, "error", message),
  warn: (jobId: string, message: string) => logToFile(jobId, "warn", message),
};

export async function handleOrderCartAttributeTaggingJob(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = getJobId(job);

  try {
    logger.info(jobId, "Starting cart attribute tagging job");

    // 🦠 BACTERIAL APPROACH - Parse and validate job data
    const parseResult = parseJobData<JobData>(job.data as string);
    if (!parseResult.isValid) {
      logger.error(jobId, `Invalid job data: ${parseResult.error}`);
      return false;
    }

    const validationResult = validateCartAttributeJobData(parseResult.data);
    if (!validationResult.isValid) {
      logger.error(jobId, `Job data validation failed: ${validationResult.error}`);
      return false;
    }

    const jobData = parseResult.data!;
    const { config } = jobData;
    const attributeName = config.cart_attribute_to_monitor;

    // 🦠 BACTERIAL APPROACH - Extract order data using focused utility
    const orderData = extractOrderData(jobData);

    if (!orderData) {
      logger.warn(jobId, "Order not found or could not be retrieved. Skipping.");
      return true; // Not a retryable error
    }

    if (jobData.order) {
      logger.info(jobId, "Using lean order data from webhook payload");
    }

    const orderId = orderData.id;
    const context = createCartAttributeContext(orderId, attributeName);
    logger.info(jobId, `Processing cart attribute tagging: ${JSON.stringify(context)}`);

    // 🦠 BACTERIAL APPROACH - Process cart attribute tagging using focused utility
    const taggingResult = processCartAttributeTagging(orderData, config);

    if (!taggingResult.shouldTag) {
      logger.info(jobId, taggingResult.reason!);
      return true;
    }

    const { tagValue } = taggingResult;

    // 🦠 BACTERIAL APPROACH - Apply tag using focused utility
    logger.info(jobId, `Adding cart attribute tag "${tagValue}" to order ${orderId}`);

    const tagResult = await addOrderTags(admin, orderId, [tagValue!]);

    if (!tagResult.success) {
      logger.error(jobId, `Failed to add tag to order ${orderId}: ${JSON.stringify(tagResult.errors)}`);
      return false;
    }

    logger.info(jobId, `Successfully added cart attribute tag to order ${orderId}`);
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(jobId, `Cart attribute tagging job failed: ${errorMessage}`);
    return false;
  }
}