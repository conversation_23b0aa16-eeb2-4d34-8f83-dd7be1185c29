import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";
import { shopifyGraphqlRequest } from "../workers/genericWorker.server";
import { updateJobStatus } from "../queues/jobQueue.server";

/**
 * Handles the job for tagging orders with discount codes.
 * @param job The job to process.
 * @param admin The Shopify Admin API client.
 * @returns Promise<boolean> indicating if the job was processed successfully.
 */
export async function orderDiscountTaggingHandler(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  try {
    logToFile(job.id, "info", "Starting processing of order discount tagging job");

    const jobData = JSON.parse(job.data);
    const { orderId, discountCodes } = jobData;

    if (!orderId || !discountCodes || !Array.isArray(discountCodes) || discountCodes.length === 0) {
      logToFile(job.id, "error", "Invalid job data: missing orderId or discountCodes");
      await updateJobStatus(job.id, "failed", "Invalid job data");
      return false;
    }
    logToFile(job.id, "info", `Processing discount tagging for order ID: ${orderId}`);

    const success = await processOrderDiscountTagging(orderId, discountCodes, admin, job.id);

    if (success) {
      logToFile(job.id, "info", `Successfully processed discount tagging for order ${orderId}`);
      await updateJobStatus(job.id, "completed");
    } else {
      logToFile(job.id, "error", `Failed to process discount tagging for order ${orderId}`);
      await updateJobStatus(job.id, "failed", "Failed to process discount tagging for order");
    }
    return success;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(job.id, "error", `Processing failed for job ${job.id}: ${errorMessage}`);
    await updateJobStatus(job.id, "failed", errorMessage);
    return false;
  }
}

/**
 * Processes discount tagging for a specific order.
 * @param orderId The Shopify order GID.
 * @param discountCodes An array of discount codes to add as tags.
 * @param admin The Shopify Admin API client.
 * @param jobId The job ID for logging.
 * @returns Promise<boolean> indicating if the tagging was successful.
 */
async function processOrderDiscountTagging(orderId: string, discountCodes: string[], admin: ShopifyUnAuthenticatedAdminClient, jobId: string): Promise<boolean> {
  const TAGS_ADD_MUTATION = `#graphql
    mutation tagsAdd($id: ID!, $tags: [String!]!) {
      tagsAdd(id: $id, tags: $tags) {
        node { id }
        userErrors { field message }
      }
    }
  `;

  try {
    const mutationResponse = await shopifyGraphqlRequest(admin, TAGS_ADD_MUTATION, {
      id: orderId,
      tags: discountCodes,
    }, jobId);

    const userErrors = mutationResponse.data?.tagsAdd?.userErrors || [];
    if (userErrors.length > 0) {
      logToFile(jobId, "error", `Failed to add tags to order ${orderId}: ${JSON.stringify(userErrors)}`);
      return false;
    }

    logToFile(jobId, "info", `Successfully added tags [${discountCodes.join(', ')}] to order ${orderId}`);
    return true;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logToFile(jobId, "error", `Error processing discount tagging for order ${orderId}: ${errorMessage}`);
    return false;
  }
}