import type { Job } from "@prisma/client";
import type { ShopifyUnAuthenticatedAdminClient } from "app/shopify.server";
import { logToFile } from "../utils/logger.server";

// 🦠 BACTERIAL IMPORTS - Using small, focused utilities
import { parseJobData, getJobId } from "../utils/job-data";
import {
  validateDiscountTaggingJobData,
  processDiscountTagging,
  createDiscountTaggingContext,
  type DiscountTaggingJobData
} from "../utils/discount-tagging";

// 🦠 BACTERIAL UTILITIES - Small, focused helper functions
const logger = {
  info: (jobId: string, message: string) => logToFile(jobId, "info", message),
  error: (jobId: string, message: string) => logToFile(jobId, "error", message),
  warn: (jobId: string, message: string) => logToFile(jobId, "warn", message),
};

export async function orderDiscountTaggingHandler(job: Job, admin: ShopifyUnAuthenticatedAdminClient): Promise<boolean> {
  const jobId = getJobId(job);

  try {
    logger.info(jobId, "Starting order discount tagging job");

    // 🦠 BACTERIAL APPROACH - Parse and validate job data
    const parseResult = parseJobData<DiscountTaggingJobData>(job.data as string);
    if (!parseResult.isValid) {
      logger.error(jobId, `Invalid job data: ${parseResult.error}`);
      return false;
    }

    const validationResult = validateDiscountTaggingJobData(parseResult.data);
    if (!validationResult.isValid) {
      logger.error(jobId, `Job data validation failed: ${validationResult.error}`);
      return false;
    }

    const { orderId, discountCodes } = validationResult.data!;

    const context = createDiscountTaggingContext(orderId, discountCodes);
    logger.info(jobId, `Processing discount tagging: ${JSON.stringify(context)}`);

    // 🦠 BACTERIAL APPROACH - Process discount tagging using focused utility
    const result = await processDiscountTagging(admin, orderId, discountCodes);

    if (!result.success) {
      logger.error(jobId, `Failed to add discount tags to order ${orderId}: ${JSON.stringify(result.errors)}`);
      return false;
    }

    logger.info(jobId, `Successfully added discount tags to order ${orderId}`);
    return true;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(jobId, `Order discount tagging job failed: ${errorMessage}`);
    return false;
  }
}

