/*
  Warnings:

  - You are about to drop the column `processedAt` on the `Job` table. All the data in the column will be lost.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Job" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "shop" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "data" TEXT NOT NULL,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "maxRetries" INTEGER NOT NULL DEFAULT 3,
    "errorMessage" TEXT,
    "completedAt" DATETIME,
    "lockedAt" DATETIME,
    "lockedById" TEXT,
    "sessionId" TEXT
);
INSERT INTO "new_Job" ("createdAt", "data", "errorMessage", "id", "lockedAt", "lockedById", "maxRetries", "retryCount", "sessionId", "shop", "status", "type", "updatedAt") SELECT "createdAt", "data", "errorMessage", "id", "lockedAt", "lockedById", "maxRetries", "retryCount", "sessionId", "shop", "status", "type", "updatedAt" FROM "Job";
DROP TABLE "Job";
ALTER TABLE "new_Job" RENAME TO "Job";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
